version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: water-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-123456}
      MYSQL_DATABASE: ${DB_NAME:-water_tracker}
      MYSQL_USER: ${DB_USERNAME:-water_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-123456}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docs/database.sql:/docker-entrypoint-initdb.d/init.sql
      - ./mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - water-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: water-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    networks:
      - water-network

  # 后端应用
  app:
    build: .
    container_name: water-backend
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-water_tracker}
      DB_USERNAME: ${DB_USERNAME:-water_user}
      DB_PASSWORD: ${DB_PASSWORD:-123456}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET: ${JWT_SECRET:-mySecretKey123456789012345678901234567890}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400}
      UPLOAD_PATH: /app/uploads/
      SMS_PROVIDER: ${SMS_PROVIDER:-aliyun}
      SMS_ACCESS_KEY: ${SMS_ACCESS_KEY:-}
      SMS_SECRET_KEY: ${SMS_SECRET_KEY:-}
      SMS_SIGN_NAME: ${SMS_SIGN_NAME:-嘉依打卡}
      SMS_TEMPLATE_CODE: ${SMS_TEMPLATE_CODE:-SMS_123456789}
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - water-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: water-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - app_uploads:/var/www/uploads
    depends_on:
      - app
    networks:
      - water-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local

networks:
  water-network:
    driver: bridge
