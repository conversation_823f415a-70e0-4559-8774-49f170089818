# 嘉依打卡 API 接口文档

**项目名称：** 嘉依打卡：元气水杯与Ta的约定  
**版本：** V1.0  
**日期：** 2025年7月30日  
**基础URL：** `https://api.watertracker.com/api/v1`

---

## 1. 接口规范

### 1.1 请求规范
- **协议：** HTTPS
- **请求方式：** GET、POST、PUT、DELETE
- **数据格式：** JSON
- **字符编码：** UTF-8
- **Content-Type：** application/json

### 1.2 响应格式
所有接口统一返回格式：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

**状态码说明：**
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权或token过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 1.3 认证方式
- **Header：** `Authorization: Bearer {token}`
- **Token获取：** 通过登录接口获取
- **Token有效期：** 7天

---

## 2. 用户认证相关接口

### 2.1 用户注册
**接口地址：** `POST /api/v1/user`  
**接口描述：** 用户手机号注册

**请求参数：**
```json
{
  "phone_number": "13800138000",
  "password": "123456",
  "verification_code": "123456",
  "nickname": "小可爱",
  "gender": "female",
  "birthday": "1995-06-15"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "注册成功",
  "data": {
    "user_id": "user_123456789",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 604800
  }
}
```

### 2.2 用户登录
**接口地址：** `POST /api/v1/user/login`  
**接口描述：** 用户登录获取token

**请求参数：**
```json
{
  "phone_number": "13800138000",
  "password": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "user_id": "user_123456789",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 604800,
    "user_info": {
      "nickname": "小可爱",
      "avatar_url": "https://example.com/avatar.jpg",
      "gender": "female",
      "couple_id": null
    }
  }
}
```

### 2.3 获取用户信息
**接口地址：** `GET /api/v1/user`  
**接口描述：** 获取当前登录用户信息

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "id": "user_123456789",
    "phone_number": "13800138000",
    "nickname": "小可爱",
    "avatar_url": "https://example.com/avatar.jpg",
    "gender": "female",
    "birthday": "1995-06-15",
    "bio": "爱喝水的小仙女",
    "couple_id": "couple_987654321",
    "create_time": "2025-07-30T10:00:00Z"
  }
}
```

### 2.4 更新用户信息
**接口地址：** `PUT /api/v1/user`  
**接口描述：** 更新用户基本信息

**请求参数：**
```json
{
  "nickname": "新昵称",
  "avatar_url": "https://example.com/new_avatar.jpg",
  "gender": "female",
  "birthday": "1995-06-15",
  "bio": "更新后的个性签名"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "updated_fields": ["nickname", "bio"]
  }
}
```

### 2.5 修改密码
**接口地址：** `PUT /api/v1/user/password`  
**接口描述：** 修改用户登录密码

**请求参数：**
```json
{
  "old_password": "123456",
  "new_password": "654321",
  "verification_code": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "密码修改成功",
  "data": null
}
```

### 2.6 发送验证码
**接口地址：** `POST /api/v1/user/sendCode`  
**接口描述：** 发送手机验证码

**请求参数：**
```json
{
  "phone_number": "13800138000",
  "type": "register"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "验证码发送成功",
  "data": {
    "expires_in": 300
  }
}
```

### 2.7 退出登录
**接口地址：** `DELETE /api/v1/user/logout`  
**接口描述：** 用户退出登录

**响应示例：**
```json
{
  "code": 200,
  "msg": "退出成功",
  "data": null
}
```

### 2.8 删除账户
**接口地址：** `DELETE /api/v1/user`  
**接口描述：** 删除用户账户

**请求参数：**
```json
{
  "password": "123456",
  "verification_code": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "账户删除成功",
  "data": null
}
```

---

## 3. 情侣绑定相关接口

### 3.1 创建情侣邀请
**接口地址：** `POST /api/v1/couple`
**接口描述：** 创建情侣邀请码

**请求参数：**
```json
{
  "couple_name": "小明&小红",
  "anniversary_date": "2024-02-14"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "邀请码创建成功",
  "data": {
    "couple_id": "couple_123456789",
    "invite_code": "LOVE2024",
    "expires_in": 86400
  }
}
```

### 3.2 接受情侣邀请
**接口地址：** `POST /api/v1/couple/accept`
**接口描述：** 通过邀请码绑定情侣关系

**请求参数：**
```json
{
  "invite_code": "LOVE2024"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "绑定成功",
  "data": {
    "couple_id": "couple_123456789",
    "partner_info": {
      "user_id": "user_987654321",
      "nickname": "小明",
      "avatar_url": "https://example.com/avatar2.jpg"
    },
    "bound_at": "2025-07-30T10:30:00Z"
  }
}
```

### 3.3 获取情侣信息
**接口地址：** `GET /api/v1/couple`
**接口描述：** 获取当前用户的情侣关系信息

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "couple_id": "couple_123456789",
    "couple_name": "小明&小红",
    "anniversary_date": "2024-02-14",
    "total_days": 168,
    "is_active": true,
    "bound_at": "2024-02-14T20:14:00Z",
    "partner_info": {
      "user_id": "user_987654321",
      "nickname": "小明",
      "avatar_url": "https://example.com/avatar2.jpg",
      "gender": "male"
    }
  }
}
```

### 3.4 更新情侣信息
**接口地址：** `PUT /api/v1/couple`
**接口描述：** 更新情侣关系信息

**请求参数：**
```json
{
  "couple_name": "新的情侣昵称",
  "anniversary_date": "2024-02-14"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "updated_fields": ["couple_name"]
  }
}
```

### 3.5 解除情侣关系
**接口地址：** `DELETE /api/v1/couple`
**接口描述：** 解除当前情侣绑定关系

**请求参数：**
```json
{
  "reason": "分手了",
  "password": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "解绑成功",
  "data": null
}
```

### 3.6 发送情侣提醒
**接口地址：** `POST /api/v1/couple/remind`
**接口描述：** 向情侣发送喝水提醒

**请求参数：**
```json
{
  "message": "该喝水啦，宝贝！",
  "type": "water_reminder"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "提醒发送成功",
  "data": {
    "notification_id": "notif_123456789"
  }
}
```

---

## 4. 喝水打卡相关接口

### 4.1 添加喝水记录
**接口地址：** `POST /api/v1/water`
**接口描述：** 添加一次喝水打卡记录

**请求参数：**
```json
{
  "amount_ml": 300,
  "cup_type": "medium",
  "temperature": "warm",
  "note": "早上第一杯水",
  "location": "家里",
  "recorded_at": "2025-07-30T08:30:00Z"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "打卡成功",
  "data": {
    "record_id": "record_123456789",
    "today_total": 1200,
    "goal_progress": 60.0,
    "is_goal_achieved": false
  }
}
```

### 4.2 获取喝水记录列表
**接口地址：** `GET /api/v1/water`
**接口描述：** 获取用户喝水记录列表

**请求参数：**
- `date`: 查询日期，格式：YYYY-MM-DD（可选）
- `page`: 页码，默认1
- `size`: 每页数量，默认20

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "total": 15,
    "page": 1,
    "size": 20,
    "records": [
      {
        "id": "record_123456789",
        "amount_ml": 300,
        "cup_type": "medium",
        "temperature": "warm",
        "note": "早上第一杯水",
        "recorded_at": "2025-07-30T08:30:00Z"
      }
    ]
  }
}
```

### 4.3 更新喝水记录
**接口地址：** `PUT /api/v1/water/{record_id}`
**接口描述：** 更新指定的喝水记录

**请求参数：**
```json
{
  "amount_ml": 350,
  "note": "修改后的备注"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "updated_fields": ["amount_ml", "note"]
  }
}
```

### 4.4 删除喝水记录
**接口地址：** `DELETE /api/v1/water/{record_id}`
**接口描述：** 删除指定的喝水记录

**响应示例：**
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```

### 4.5 设置每日目标
**接口地址：** `POST /api/v1/water/goal`
**接口描述：** 设置每日喝水目标

**请求参数：**
```json
{
  "daily_goal_ml": 2500,
  "goal_date": "2025-07-30"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "目标设置成功",
  "data": {
    "goal_id": "goal_123456789",
    "daily_goal_ml": 2500
  }
}
```

### 4.6 获取每日目标
**接口地址：** `GET /api/v1/water/goal`
**接口描述：** 获取指定日期的喝水目标

**请求参数：**
- `date`: 查询日期，格式：YYYY-MM-DD（可选，默认今天）

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "goal_id": "goal_123456789",
    "daily_goal_ml": 2500,
    "actual_amount_ml": 1200,
    "completion_rate": 48.0,
    "is_achieved": false,
    "goal_date": "2025-07-30"
  }
}
```

### 4.7 获取统计数据
**接口地址：** `GET /api/v1/water/statistics`
**接口描述：** 获取喝水统计数据

**请求参数：**
- `type`: 统计类型，daily/weekly/monthly（必填）
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "type": "weekly",
    "period": "2025-07-24 ~ 2025-07-30",
    "total_amount": 14500,
    "avg_daily_amount": 2071,
    "goal_achieved_days": 5,
    "total_days": 7,
    "achievement_rate": 71.4,
    "daily_data": [
      {
        "date": "2025-07-24",
        "amount": 2200,
        "goal": 2000,
        "achieved": true
      }
    ]
  }
}
```

### 4.8 获取今日进度
**接口地址：** `GET /api/v1/water/todayProgress`
**接口描述：** 获取今日喝水进度详情

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "today_total": 1200,
    "daily_goal": 2000,
    "completion_rate": 60.0,
    "remaining": 800,
    "is_achieved": false,
    "records_count": 4,
    "last_record_time": "2025-07-30T14:30:00Z",
    "next_reminder": "2025-07-30T16:00:00Z"
  }
}
```

---

## 5. 视频相关接口

### 5.1 上传视频
**接口地址：** `POST /api/v1/video`
**接口描述：** 上传喝水视频

**请求参数：** (multipart/form-data)
- `video_file`: 视频文件
- `thumbnail_file`: 缩略图文件（可选）
- `description`: 视频描述
- `visibility`: 可见范围（private/couple/public）
- `filter_name`: 滤镜名称（可选）
- `music_name`: 背景音乐（可选）
- `tags`: 标签，逗号分隔（可选）

**响应示例：**
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "video_id": "video_123456789",
    "video_url": "https://cdn.example.com/videos/123456789.mp4",
    "thumbnail_url": "https://cdn.example.com/thumbnails/123456789.jpg",
    "duration_seconds": 15,
    "status": "processing"
  }
}
```

### 5.2 获取视频列表
**接口地址：** `GET /api/v1/video`
**接口描述：** 获取视频列表

**请求参数：**
- `type`: 视频类型，my/couple/public（默认couple）
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "videos": [
      {
        "id": "video_123456789",
        "user_id": "user_987654321",
        "user_info": {
          "nickname": "小明",
          "avatar_url": "https://example.com/avatar.jpg"
        },
        "video_url": "https://cdn.example.com/videos/123456789.mp4",
        "thumbnail_url": "https://cdn.example.com/thumbnails/123456789.jpg",
        "description": "今天的第一杯水",
        "duration_seconds": 15,
        "like_count": 5,
        "comment_count": 2,
        "view_count": 20,
        "is_liked": true,
        "create_time": "2025-07-30T10:00:00Z"
      }
    ]
  }
}
```

### 5.3 获取视频详情
**接口地址：** `GET /api/v1/video/{video_id}`
**接口描述：** 获取指定视频详情

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "id": "video_123456789",
    "user_id": "user_987654321",
    "user_info": {
      "nickname": "小明",
      "avatar_url": "https://example.com/avatar.jpg"
    },
    "video_url": "https://cdn.example.com/videos/123456789.mp4",
    "thumbnail_url": "https://cdn.example.com/thumbnails/123456789.jpg",
    "description": "今天的第一杯水",
    "visibility": "couple",
    "duration_seconds": 15,
    "like_count": 5,
    "comment_count": 2,
    "view_count": 21,
    "is_liked": true,
    "filter_name": "sweet_pink",
    "music_name": "轻快音乐",
    "tags": ["早晨", "第一杯水"],
    "create_time": "2025-07-30T10:00:00Z"
  }
}
```

### 5.4 点赞/取消点赞视频
**接口地址：** `POST /api/v1/video/{video_id}/like`
**接口描述：** 点赞或取消点赞视频

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "is_liked": true,
    "like_count": 6
  }
}
```

### 5.5 评论视频
**接口地址：** `POST /api/v1/video/{video_id}/comment`
**接口描述：** 对视频进行评论

**请求参数：**
```json
{
  "comment_text": "好可爱的视频！",
  "reply_to_id": null,
  "emoji_type": "heart"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "评论成功",
  "data": {
    "comment_id": "comment_123456789",
    "comment_count": 3
  }
}
```

### 5.6 获取视频评论
**接口地址：** `GET /api/v1/video/{video_id}/comment`
**接口描述：** 获取视频评论列表

**请求参数：**
- `page`: 页码，默认1
- `size`: 每页数量，默认20

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "total": 3,
    "page": 1,
    "size": 20,
    "comments": [
      {
        "id": "comment_123456789",
        "user_id": "user_987654321",
        "user_info": {
          "nickname": "小红",
          "avatar_url": "https://example.com/avatar2.jpg"
        },
        "comment_text": "好可爱的视频！",
        "emoji_type": "heart",
        "reply_to_id": null,
        "create_time": "2025-07-30T10:30:00Z"
      }
    ]
  }
}
```

### 5.7 删除视频
**接口地址：** `DELETE /api/v1/video/{video_id}`
**接口描述：** 删除自己的视频

**响应示例：**
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```

### 5.8 举报视频
**接口地址：** `POST /api/v1/video/{video_id}/report`
**接口描述：** 举报不当视频

**请求参数：**
```json
{
  "reason": "内容不当",
  "description": "详细描述举报原因"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "举报成功",
  "data": {
    "report_id": "report_123456789"
  }
}
```

---

## 6. 成就系统相关接口

### 6.1 获取成就列表
**接口地址：** `GET /api/v1/achievement`
**接口描述：** 获取所有成就列表

**请求参数：**
- `type`: 成就类型，personal/couple/system（可选）
- `category`: 成就分类，water/video/social（可选）

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "achievements": [
      {
        "id": "ach_first_checkin",
        "name": "初次打卡",
        "description": "完成第一次喝水打卡",
        "icon_url": "https://example.com/icons/first_checkin.png",
        "type": "personal",
        "category": "water",
        "points": 10,
        "rarity": "common",
        "is_unlocked": true,
        "unlocked_at": "2025-07-30T08:00:00Z",
        "progress": 100.0
      }
    ]
  }
}
```

### 6.2 获取用户成就
**接口地址：** `GET /api/v1/achievement/my`
**接口描述：** 获取当前用户的成就情况

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "total_achievements": 15,
    "unlocked_count": 5,
    "total_points": 150,
    "recent_unlocked": [
      {
        "achievement_id": "ach_week_streak",
        "name": "坚持一周",
        "unlocked_at": "2025-07-30T10:00:00Z"
      }
    ]
  }
}
```

---

## 7. 挑战功能相关接口

### 7.1 创建挑战
**接口地址：** `POST /api/v1/challenge`
**接口描述：** 创建情侣挑战

**请求参数：**
```json
{
  "name": "7天喝水挑战",
  "description": "连续7天每天完成喝水目标",
  "type": "water",
  "duration_days": 7,
  "requirements": {
    "daily_goal": 2000,
    "consecutive_days": 7
  },
  "start_date": "2025-08-01"
}
```

### 7.2 获取挑战列表
**接口地址：** `GET /api/v1/challenge`
**接口描述：** 获取情侣挑战列表

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "challenges": [
      {
        "id": "challenge_123456789",
        "name": "7天喝水挑战",
        "status": "active",
        "start_date": "2025-08-01",
        "end_date": "2025-08-07",
        "user1_progress": 85.7,
        "user2_progress": 71.4,
        "total_progress": 78.6
      }
    ]
  }
}
```

---

## 8. 通知和提醒相关接口

### 8.1 获取通知列表
**接口地址：** `GET /api/v1/notification`
**接口描述：** 获取用户通知列表

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "unread_count": 3,
    "notifications": [
      {
        "id": "notif_123456789",
        "type": "achievement",
        "title": "恭喜解锁新成就",
        "message": "您已解锁"坚持一周"成就！",
        "is_read": false,
        "create_time": "2025-07-30T10:00:00Z"
      }
    ]
  }
}
```

### 8.2 设置提醒
**接口地址：** `POST /api/v1/reminder`
**接口描述：** 设置喝水提醒

**请求参数：**
```json
{
  "reminder_time": "09:00",
  "message": "该喝水啦！",
  "repeat_days": "1,2,3,4,5,6,7",
  "sound_type": "bubble"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "提醒设置成功",
  "data": {
    "reminder_id": "reminder_123456789"
  }
}
```
