package com.maike.api.video.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class VideoVO {

    /**
     * 视频ID
     */
    private String id;

    /**
     * 视频文件URL地址
     */
    private String videoUrl;

    /**
     * 视频缩略图URL地址
     */
    private String thumbnailUrl;

    /**
     * 视频描述
     */
    private String description;

    /**
     * 可见范围：private-仅自己，couple-情侣可见，public-公开
     */
    private String visibility;

    /**
     * 可见范围显示文本
     */
    private String visibilityText;

    /**
     * 视频时长，单位秒
     */
    private Integer durationSeconds;

    /**
     * 格式化后的时长字符串
     */
    private String formattedDuration;

    /**
     * 文件大小，单位字节
     */
    private Long fileSize;

    /**
     * 格式化后的文件大小字符串
     */
    private String formattedFileSize;

    /**
     * 视频宽度，像素
     */
    private Integer width;

    /**
     * 视频高度，像素
     */
    private Integer height;

    /**
     * 视频格式
     */
    private String format;

    /**
     * 使用的滤镜名称
     */
    private String filterName;

    /**
     * 背景音乐名称
     */
    private String musicName;

    /**
     * 视频标签列表
     */
    private List<String> tags;

    /**
     * 点赞数量
     */
    private Integer likeCount;

    /**
     * 评论数量
     */
    private Integer commentCount;

    /**
     * 播放次数
     */
    private Integer viewCount;

    /**
     * 是否精选
     */
    private Boolean isFeatured;

    /**
     * 当前用户是否已点赞
     */
    private Boolean isLiked;

    /**
     * 视频状态
     */
    private String status;

    /**
     * 状态显示文本
     */
    private String statusText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 作者信息
     */
    private AuthorInfo authorInfo;

    /**
     * 作者信息内部类
     */
    @Data
    public static class AuthorInfo {
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 头像URL
         */
        private String avatarUrl;

        /**
         * 性别
         */
        private String gender;
    }
}
