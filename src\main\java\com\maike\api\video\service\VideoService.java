package com.maike.api.video.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.video.pojo.dto.VideoUploadDTO;
import com.maike.api.video.pojo.entity.Video;
import com.maike.api.video.pojo.vo.VideoVO;

import java.util.List;

/**
 * 视频服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface VideoService extends IService<Video> {

    /**
     * 上传视频
     * 
     * @param userId    用户ID
     * @param uploadDTO 上传信息
     * @return 视频信息
     */
    VideoVO uploadVideo(String userId, VideoUploadDTO uploadDTO);

    /**
     * 分页查询视频列表
     * 
     * @param userId 用户ID
     * @param type   类型：my-我的，couple-情侣，public-公开，featured-精选
     * @param page   页码
     * @param size   每页数量
     * @return 分页结果
     */
    IPage<VideoVO> getVideoList(String userId, String type, Integer page, Integer size);

    /**
     * 获取视频详情
     * 
     * @param userId  用户ID
     * @param videoId 视频ID
     * @return 视频详情
     */
    VideoVO getVideoDetail(String userId, String videoId);

    /**
     * 删除视频
     * 
     * @param userId  用户ID
     * @param videoId 视频ID
     */
    void deleteVideo(String userId, String videoId);

    /**
     * 点赞/取消点赞视频
     * 
     * @param userId  用户ID
     * @param videoId 视频ID
     * @return 点赞结果
     */
    LikeResultVO toggleLike(String userId, String videoId);

    /**
     * 增加播放次数
     * 
     * @param videoId 视频ID
     */
    void incrementViewCount(String videoId);

    /**
     * 获取用户最近的视频
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 视频列表
     */
    List<VideoVO> getUserRecentVideos(String userId, Integer limit);

    /**
     * 获取情侣最近的视频
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 视频列表
     */
    List<VideoVO> getCoupleRecentVideos(String userId, Integer limit);

    /**
     * 根据标签搜索视频
     * 
     * @param userId 用户ID
     * @param tag    标签
     * @param page   页码
     * @param size   每页数量
     * @return 分页结果
     */
    IPage<VideoVO> searchVideosByTag(String userId, String tag, Integer page, Integer size);

    /**
     * 获取视频统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    VideoStatsVO getVideoStats(String userId);

    /**
     * 点赞结果VO
     */
    class LikeResultVO {
        private Boolean isLiked;
        private Integer likeCount;

        // getters and setters
        public Boolean getIsLiked() { return isLiked; }
        public void setIsLiked(Boolean isLiked) { this.isLiked = isLiked; }
        public Integer getLikeCount() { return likeCount; }
        public void setLikeCount(Integer likeCount) { this.likeCount = likeCount; }
    }

    /**
     * 视频统计VO
     */
    class VideoStatsVO {
        private Integer totalVideos;
        private Integer totalLikes;
        private Integer totalViews;
        private Integer totalComments;

        // getters and setters
        public Integer getTotalVideos() { return totalVideos; }
        public void setTotalVideos(Integer totalVideos) { this.totalVideos = totalVideos; }
        public Integer getTotalLikes() { return totalLikes; }
        public void setTotalLikes(Integer totalLikes) { this.totalLikes = totalLikes; }
        public Integer getTotalViews() { return totalViews; }
        public void setTotalViews(Integer totalViews) { this.totalViews = totalViews; }
        public Integer getTotalComments() { return totalComments; }
        public void setTotalComments(Integer totalComments) { this.totalComments = totalComments; }
    }
}
