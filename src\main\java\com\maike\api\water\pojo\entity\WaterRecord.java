package com.maike.api.water.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 喝水记录实体类
 * 对应数据库表：water_records
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("water_records")
public class WaterRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 喝水量，单位毫升(ml)
     */
    @TableField("amount_ml")
    private Integer amountMl;

    /**
     * 记录时间，用户打卡的具体时间
     */
    @TableField("recorded_at")
    private LocalDateTime recordedAt;

    /**
     * 杯子类型：default-默认，small-小杯，medium-中杯，large-大杯
     */
    @TableField("cup_type")
    private String cupType;

    /**
     * 杯子容量，单位毫升，用于快捷打卡
     */
    @TableField("cup_size_ml")
    private Integer cupSizeMl;

    /**
     * 喝水备注，用户可添加心情或说明
     */
    @TableField("note")
    private String note;

    /**
     * 打卡地点，可选记录
     */
    @TableField("location")
    private String location;

    /**
     * 水温：hot-热水，warm-温水，cold-凉水，ice-冰水
     */
    @TableField("temperature")
    private String temperature;

    /**
     * 记录状态：0-正常，1-已删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断记录是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断记录是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.status);
    }

    /**
     * 获取水温显示文本
     * 
     * @return 水温显示文本
     */
    public String getTemperatureText() {
        if (temperature == null) {
            return "常温";
        }
        
        switch (temperature) {
            case "hot":
                return "热水";
            case "warm":
                return "温水";
            case "cold":
                return "凉水";
            case "ice":
                return "冰水";
            default:
                return "常温";
        }
    }

    /**
     * 获取杯子类型显示文本
     * 
     * @return 杯子类型显示文本
     */
    public String getCupTypeText() {
        if (cupType == null) {
            return "默认";
        }
        
        switch (cupType) {
            case "small":
                return "小杯";
            case "medium":
                return "中杯";
            case "large":
                return "大杯";
            case "bottle":
                return "水瓶";
            default:
                return "默认";
        }
    }
}
