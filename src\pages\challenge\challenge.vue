<template>
  <view class="challenge-page">
    <!-- 顶部导航 -->
    <view class="challenge-nav">
      <view class="nav-tabs">
        <view 
          class="nav-tab" 
          v-for="tab in navTabs" 
          :key="tab.key"
          :class="{ active: currentTab === tab.key }"
          @click="switchTab(tab.key)"
        >
          <text>{{ tab.label }}</text>
        </view>
      </view>
      
      <view class="create-btn" @click="createChallenge">
        <image class="create-icon" src="/static/icons/plus.png"></image>
      </view>
    </view>

    <!-- 挑战列表 -->
    <view class="challenge-list">
      <view class="challenge-item" v-for="challenge in challengeList" :key="challenge.id" @click="viewChallenge(challenge)">
        <view class="challenge-header">
          <view class="challenge-info">
            <text class="challenge-name">{{ challenge.name }}</text>
            <text class="challenge-desc">{{ challenge.description }}</text>
          </view>
          
          <view class="challenge-status" :class="challenge.status">
            <text>{{ getStatusText(challenge.status) }}</text>
          </view>
        </view>
        
        <view class="challenge-progress">
          <view class="progress-info">
            <text class="progress-label">总体进度</text>
            <text class="progress-percent">{{ challenge.total_progress }}%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: challenge.total_progress + '%' }"></view>
          </view>
        </view>
        
        <view class="challenge-participants">
          <view class="participant">
            <image class="participant-avatar" :src="userInfo.avatar_url" mode="aspectFill"></image>
            <view class="participant-progress">
              <text class="participant-name">{{ userInfo.nickname }}</text>
              <view class="mini-progress-bar">
                <view class="mini-progress-fill" :style="{ width: challenge.user1_progress + '%' }"></view>
              </view>
            </view>
          </view>
          
          <view class="participant">
            <image class="participant-avatar" :src="challenge.partner_avatar" mode="aspectFill"></image>
            <view class="participant-progress">
              <text class="participant-name">{{ challenge.partner_name }}</text>
              <view class="mini-progress-bar">
                <view class="mini-progress-fill" :style="{ width: challenge.user2_progress + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="challenge-footer">
          <view class="challenge-time">
            <text>{{ formatDateRange(challenge.start_date, challenge.end_date) }}</text>
          </view>
          
          <view class="challenge-actions">
            <view class="action-btn" v-if="challenge.status === 'active'" @click.stop="updateProgress(challenge)">
              <text>更新进度</text>
            </view>
            <view class="action-btn secondary" @click.stop="shareChallenge(challenge)">
              <text>分享</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="challengeList.length === 0 && !loading">
        <image class="empty-image" src="/static/illustrations/challenge-empty.png" mode="aspectFit"></image>
        <text class="empty-text">还没有挑战</text>
        <text class="empty-desc">创建第一个挑战，和TA一起努力吧！</text>
        <button class="create-first-btn" @click="createChallenge">创建挑战</button>
      </view>
    </view>

    <!-- 创建挑战弹窗 -->
    <uni-popup ref="createChallengePopup" type="bottom">
      <view class="create-challenge-container">
        <view class="create-header">
          <text class="create-title">创建挑战</text>
          <view class="close-create" @click="closeCreateChallenge">
            <image class="close-icon" src="/static/icons/close.png"></image>
          </view>
        </view>
        
        <view class="create-form">
          <view class="form-item">
            <text class="form-label">挑战名称</text>
            <input class="form-input" v-model="newChallenge.name" placeholder="给挑战起个名字" />
          </view>
          
          <view class="form-item">
            <text class="form-label">挑战描述</text>
            <textarea class="form-textarea" v-model="newChallenge.description" placeholder="描述一下挑战内容" />
          </view>
          
          <view class="form-item">
            <text class="form-label">挑战类型</text>
            <view class="type-options">
              <view 
                class="type-option" 
                v-for="type in challengeTypes" 
                :key="type.key"
                :class="{ active: newChallenge.type === type.key }"
                @click="newChallenge.type = type.key"
              >
                <image class="type-icon" :src="type.icon"></image>
                <text class="type-text">{{ type.label }}</text>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">持续天数</text>
            <view class="duration-options">
              <view 
                class="duration-option" 
                v-for="duration in durationOptions" 
                :key="duration"
                :class="{ active: newChallenge.duration_days === duration }"
                @click="newChallenge.duration_days = duration"
              >
                <text>{{ duration }}天</text>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">开始日期</text>
            <picker mode="date" :value="newChallenge.start_date" @change="onDateChange">
              <view class="date-picker">
                <text>{{ newChallenge.start_date || '选择开始日期' }}</text>
                <image class="picker-icon" src="/static/icons/calendar.png"></image>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="create-actions">
          <button class="cancel-create-btn" @click="closeCreateChallenge">取消</button>
          <button class="confirm-create-btn" @click="submitChallenge" :disabled="!canCreateChallenge">创建</button>
        </view>
      </view>
    </uni-popup>

    <!-- 挑战详情弹窗 -->
    <uni-popup ref="challengeDetailPopup" type="center">
      <view class="challenge-detail-popup">
        <view class="detail-header">
          <text class="detail-title">{{ selectedChallenge.name }}</text>
          <view class="detail-status" :class="selectedChallenge.status">
            <text>{{ getStatusText(selectedChallenge.status) }}</text>
          </view>
        </view>
        
        <view class="detail-content">
          <text class="detail-desc">{{ selectedChallenge.description }}</text>
          
          <view class="detail-progress">
            <text class="progress-title">挑战进度</text>
            <view class="progress-chart">
              <view class="chart-item">
                <text class="chart-label">{{ userInfo.nickname }}</text>
                <view class="chart-bar">
                  <view class="chart-fill" :style="{ width: selectedChallenge.user1_progress + '%' }"></view>
                </view>
                <text class="chart-percent">{{ selectedChallenge.user1_progress }}%</text>
              </view>
              
              <view class="chart-item">
                <text class="chart-label">{{ selectedChallenge.partner_name }}</text>
                <view class="chart-bar">
                  <view class="chart-fill" :style="{ width: selectedChallenge.user2_progress + '%' }"></view>
                </view>
                <text class="chart-percent">{{ selectedChallenge.user2_progress }}%</text>
              </view>
            </view>
          </view>
          
          <view class="detail-info">
            <view class="info-row">
              <text class="info-label">挑战类型</text>
              <text class="info-value">{{ getChallengeTypeText(selectedChallenge.type) }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">持续时间</text>
              <text class="info-value">{{ selectedChallenge.duration_days }}天</text>
            </view>
            <view class="info-row">
              <text class="info-label">开始时间</text>
              <text class="info-value">{{ formatDate(selectedChallenge.start_date) }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">结束时间</text>
              <text class="info-value">{{ formatDate(selectedChallenge.end_date) }}</text>
            </view>
          </view>
        </view>
        
        <view class="detail-actions">
          <button class="close-detail-btn" @click="closeChallengeDetail">关闭</button>
          <button 
            class="update-progress-btn" 
            v-if="selectedChallenge.status === 'active'"
            @click="updateChallengeProgress"
          >
            更新进度
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import request from '@/utils/request'

// 响应式数据
const currentTab = ref('active')
const challengeList = ref([])
const selectedChallenge = ref({})
const loading = ref(false)

const userInfo = ref({
  nickname: '',
  avatar_url: ''
})

const newChallenge = ref({
  name: '',
  description: '',
  type: 'water',
  duration_days: 7,
  start_date: ''
})

// 导航标签
const navTabs = ref([
  { key: 'active', label: '进行中' },
  { key: 'completed', label: '已完成' },
  { key: 'all', label: '全部' }
])

// 挑战类型
const challengeTypes = ref([
  { key: 'water', label: '喝水挑战', icon: '/static/icons/water-drop.png' },
  { key: 'video', label: '视频挑战', icon: '/static/icons/video-camera.png' },
  { key: 'interaction', label: '互动挑战', icon: '/static/icons/heart.png' },
  { key: 'custom', label: '自定义', icon: '/static/icons/custom.png' }
])

// 持续天数选项
const durationOptions = ref([3, 7, 14, 21, 30])

// 弹窗引用
const createChallengePopup = ref()
const challengeDetailPopup = ref()

// 计算属性：是否可以创建挑战
const canCreateChallenge = computed(() => {
  return newChallenge.value.name.trim() && 
         newChallenge.value.description.trim() && 
         newChallenge.value.start_date
})

// 页面加载时获取数据
onMounted(() => {
  loadUserInfo()
  loadChallengeList()
  
  // 设置默认开始日期为明天
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  newChallenge.value.start_date = tomorrow.toISOString().split('T')[0]
})

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await request.get('/user')
    userInfo.value = response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取挑战列表
const loadChallengeList = async () => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    const response = await request.get('/challenge', {
      status: currentTab.value === 'all' ? undefined : currentTab.value
    })
    challengeList.value = response.data.challenges
  } catch (error) {
    console.error('获取挑战列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 切换标签
const switchTab = (tabKey: string) => {
  currentTab.value = tabKey
  loadChallengeList()
}

// 创建挑战
const createChallenge = () => {
  createChallengePopup.value.open()
}

// 关闭创建挑战弹窗
const closeCreateChallenge = () => {
  createChallengePopup.value.close()
  // 重置表单
  newChallenge.value = {
    name: '',
    description: '',
    type: 'water',
    duration_days: 7,
    start_date: new Date(Date.now() + 86400000).toISOString().split('T')[0]
  }
}

// 日期选择
const onDateChange = (e: any) => {
  newChallenge.value.start_date = e.detail.value
}

// 提交挑战
const submitChallenge = async () => {
  if (!canCreateChallenge.value) return
  
  try {
    await request.post('/challenge', newChallenge.value)
    
    closeCreateChallenge()
    loadChallengeList()
    
    uni.showToast({
      title: '挑战创建成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('创建挑战失败:', error)
    uni.showToast({
      title: '创建失败，请重试',
      icon: 'error'
    })
  }
}

// 查看挑战详情
const viewChallenge = (challenge: any) => {
  selectedChallenge.value = challenge
  challengeDetailPopup.value.open()
}

// 关闭挑战详情
const closeChallengeDetail = () => {
  challengeDetailPopup.value.close()
}

// 更新挑战进度
const updateChallengeProgress = () => {
  // 这里可以跳转到进度更新页面或显示更新弹窗
  uni.navigateTo({
    url: `/pages/challenge/progress?id=${selectedChallenge.value.id}`
  })
}

// 更新进度
const updateProgress = (challenge: any) => {
  updateChallengeProgress()
}

// 分享挑战
const shareChallenge = (challenge: any) => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '',
    title: '嘉依打卡挑战分享',
    summary: `我和TA正在进行"${challenge.name}"挑战，一起来加油吧！`,
    imageUrl: '/static/share-challenge.png'
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待开始',
    active: '进行中',
    completed: '已完成',
    failed: '已失败',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取挑战类型文本
const getChallengeTypeText = (type: string) => {
  const typeMap = {
    water: '喝水挑战',
    video: '视频挑战',
    interaction: '互动挑战',
    custom: '自定义挑战'
  }
  return typeMap[type] || '未知类型'
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`
}

// 格式化日期范围
const formatDateRange = (startDate: string, endDate: string) => {
  return `${formatDate(startDate)} - ${formatDate(endDate)}`
}
</script>

<style lang="scss" scoped>
.challenge-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
}

/* 顶部导航样式 */
.challenge-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.1);
}

.nav-tabs {
  display: flex;
  gap: 32rpx;
}

.nav-tab {
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.nav-tab.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
}

.nav-tab:not(.active) {
  color: #666;
}

.nav-tab text {
  font-size: 28rpx;
  font-weight: bold;
}

.create-btn {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
}

.create-btn:active {
  transform: scale(0.95);
}

.create-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 挑战列表样式 */
.challenge-list {
  padding: 0 32rpx 32rpx;
}

.challenge-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
}

.challenge-item:active {
  transform: scale(0.98);
}

.challenge-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.challenge-info {
  flex: 1;
  margin-right: 16rpx;
}

.challenge-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.challenge-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.challenge-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.challenge-status.pending {
  background: #FFE4B5;
  color: #FF8C00;
}

.challenge-status.active {
  background: #98FB98;
  color: #228B22;
}

.challenge-status.completed {
  background: #87CEEB;
  color: #4682B4;
}

.challenge-status.failed {
  background: #FFA07A;
  color: #DC143C;
}

.challenge-progress {
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #666;
}

.progress-percent {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

.progress-bar {
  height: 12rpx;
  background: #FFE4E1;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.challenge-participants {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.participant {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.participant-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
}

.participant-progress {
  flex: 1;
}

.participant-name {
  display: block;
  font-size: 20rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.mini-progress-bar {
  height: 6rpx;
  background: #FFE4E1;
  border-radius: 3rpx;
  overflow: hidden;
}

.mini-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.challenge-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.challenge-time {
  font-size: 20rpx;
  color: #999;
}

.challenge-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #F5F5F5;
  color: #666;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 32rpx;
}

.create-first-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

/* 创建挑战弹窗样式 */
.create-challenge-container {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.create-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.create-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-create {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}

.create-form {
  margin-bottom: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.form-input, .form-textarea {
  width: 100%;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid #FFE4E1;
}

.form-textarea {
  min-height: 120rpx;
}

.type-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #FFF0F5;
  border-radius: 16rpx;
  border: 2rpx solid #FFE4E1;
  transition: all 0.3s ease;
}

.type-option.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border-color: #FF69B4;
}

.type-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
}

.type-text {
  font-size: 24rpx;
  font-weight: bold;
}

.duration-options {
  display: flex;
  gap: 16rpx;
}

.duration-option {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  background: #FFF0F5;
  border-radius: 12rpx;
  border: 2rpx solid #FFE4E1;
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  transition: all 0.3s ease;
}

.duration-option.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border-color: #FF69B4;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #FFE4E1;
}

.picker-icon {
  width: 24rpx;
  height: 24rpx;
}

.create-actions {
  display: flex;
  gap: 24rpx;
}

.cancel-create-btn, .confirm-create-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.cancel-create-btn {
  background: #F5F5F5;
  color: #666;
}

.confirm-create-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

.confirm-create-btn:disabled {
  background: #E0E0E0;
  color: #999;
  box-shadow: none;
}

/* 挑战详情弹窗样式 */
.challenge-detail-popup {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  width: 85vw;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.detail-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.detail-content {
  margin-bottom: 32rpx;
}

.detail-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.detail-progress {
  margin-bottom: 32rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.progress-chart {
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.chart-item:last-child {
  margin-bottom: 0;
}

.chart-label {
  min-width: 120rpx;
  font-size: 24rpx;
  color: #333;
}

.chart-bar {
  flex: 1;
  height: 16rpx;
  background: #FFE4E1;
  border-radius: 8rpx;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.chart-percent {
  min-width: 80rpx;
  text-align: right;
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

.detail-info {
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #999;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.detail-actions {
  display: flex;
  gap: 24rpx;
}

.close-detail-btn, .update-progress-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.close-detail-btn {
  background: #F5F5F5;
  color: #666;
}

.update-progress-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}
</style>
