package com.maike.api.video.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频实体类
 * 对应数据库表：videos
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("videos")
public class Video implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 视频ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 视频文件URL地址
     */
    @TableField("video_url")
    private String videoUrl;

    /**
     * 视频缩略图URL地址
     */
    @TableField("thumbnail_url")
    private String thumbnailUrl;

    /**
     * 视频描述，用户添加的文字说明
     */
    @TableField("description")
    private String description;

    /**
     * 可见范围：private-仅自己，couple-情侣可见，public-公开
     */
    @TableField("visibility")
    private String visibility;

    /**
     * 视频时长，单位秒
     */
    @TableField("duration_seconds")
    private Integer durationSeconds;

    /**
     * 文件大小，单位字节
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 视频宽度，像素
     */
    @TableField("width")
    private Integer width;

    /**
     * 视频高度，像素
     */
    @TableField("height")
    private Integer height;

    /**
     * 视频格式：mp4、mov等
     */
    @TableField("format")
    private String format;

    /**
     * 使用的滤镜名称
     */
    @TableField("filter_name")
    private String filterName;

    /**
     * 背景音乐名称
     */
    @TableField("music_name")
    private String musicName;

    /**
     * 视频标签，逗号分隔
     */
    @TableField("tags")
    private String tags;

    /**
     * 点赞数量
     */
    @TableField("like_count")
    private Integer likeCount;

    /**
     * 评论数量
     */
    @TableField("comment_count")
    private Integer commentCount;

    /**
     * 播放次数
     */
    @TableField("view_count")
    private Integer viewCount;

    /**
     * 是否精选：1-是，0-否
     */
    @TableField("is_featured")
    private Boolean isFeatured;

    /**
     * 视频状态：0-正常，1-审核中，2-已删除，3-违规
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "2")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断视频是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断视频是否审核中
     * 
     * @return true-审核中，false-非审核中
     */
    public boolean isReviewing() {
        return "1".equals(this.status);
    }

    /**
     * 判断视频是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "2".equals(this.status);
    }

    /**
     * 判断视频是否违规
     * 
     * @return true-违规，false-正常
     */
    public boolean isViolated() {
        return "3".equals(this.status);
    }

    /**
     * 获取可见范围显示文本
     * 
     * @return 可见范围显示文本
     */
    public String getVisibilityText() {
        if (visibility == null) {
            return "情侣可见";
        }
        
        switch (visibility) {
            case "private":
                return "仅自己可见";
            case "couple":
                return "情侣可见";
            case "public":
                return "公开";
            default:
                return "情侣可见";
        }
    }

    /**
     * 获取状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusText() {
        if (status == null) {
            return "正常";
        }
        
        switch (status) {
            case "0":
                return "正常";
            case "1":
                return "审核中";
            case "2":
                return "已删除";
            case "3":
                return "违规";
            default:
                return "正常";
        }
    }

    /**
     * 获取标签列表
     * 
     * @return 标签列表
     */
    public List<String> getTagsList() {
        if (tags == null || tags.trim().isEmpty()) {
            return Arrays.asList();
        }
        
        return Arrays.stream(tags.split(","))
                .map(String::trim)
                .filter(tag -> !tag.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 设置标签列表
     * 
     * @param tagsList 标签列表
     */
    public void setTagsList(List<String> tagsList) {
        if (tagsList == null || tagsList.isEmpty()) {
            this.tags = null;
        } else {
            this.tags = tagsList.stream()
                    .filter(tag -> tag != null && !tag.trim().isEmpty())
                    .map(String::trim)
                    .collect(Collectors.joining(","));
        }
    }

    /**
     * 格式化视频时长
     * 
     * @return 格式化后的时长字符串，如"1:23"
     */
    public String getFormattedDuration() {
        if (durationSeconds == null || durationSeconds <= 0) {
            return "0:00";
        }
        
        int minutes = durationSeconds / 60;
        int seconds = durationSeconds % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    /**
     * 格式化文件大小
     * 
     * @return 格式化后的文件大小字符串，如"1.2MB"
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0B";
        }
        
        String[] units = {"B", "KB", "MB", "GB"};
        double size = fileSize.doubleValue();
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f%s", size, units[unitIndex]);
    }

    /**
     * 增加点赞数
     */
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0 : this.likeCount) + 1;
    }

    /**
     * 减少点赞数
     */
    public void decrementLikeCount() {
        this.likeCount = Math.max(0, (this.likeCount == null ? 0 : this.likeCount) - 1);
    }

    /**
     * 增加评论数
     */
    public void incrementCommentCount() {
        this.commentCount = (this.commentCount == null ? 0 : this.commentCount) + 1;
    }

    /**
     * 减少评论数
     */
    public void decrementCommentCount() {
        this.commentCount = Math.max(0, (this.commentCount == null ? 0 : this.commentCount) - 1);
    }

    /**
     * 增加播放次数
     */
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
    }
}
