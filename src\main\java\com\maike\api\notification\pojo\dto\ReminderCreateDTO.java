package com.maike.api.notification.pojo.dto;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

/**
 * 创建提醒请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ReminderCreateDTO {

    /**
     * 提醒时间，如09:00、14:30
     */
    @NotNull(message = "提醒时间不能为空")
    private LocalTime reminderTime;

    /**
     * 提醒消息内容
     */
    @Size(max = 200, message = "提醒消息长度不能超过200个字符")
    private String message;

    /**
     * 是否启用：true-启用，false-禁用
     */
    private Boolean isActive;

    /**
     * 提醒音效类型：default-默认，bubble-泡泡音，sweet-甜美音
     */
    @Pattern(regexp = "^(default|bubble|sweet)$", message = "音效类型必须是default、bubble或sweet")
    private String soundType;

    /**
     * 重复日期列表，1-7表示周一到周日
     */
    private List<Integer> repeatDays;

    /**
     * 延迟提醒分钟数
     */
    @Min(value = 1, message = "延迟提醒分钟数不能小于1分钟")
    @Max(value = 60, message = "延迟提醒分钟数不能超过60分钟")
    private Integer snoozeMinutes;

    /**
     * 提醒优先级：1-低，2-中，3-高
     */
    @Min(value = 1, message = "优先级必须在1-3之间")
    @Max(value = 3, message = "优先级必须在1-3之间")
    private Integer priority;
}
