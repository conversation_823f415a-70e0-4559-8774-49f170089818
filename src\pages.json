{
	"easycom": {
		"autoscan": true,
		"custom": {
			// uni-ui 规则如下配置
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
		}
	},

	"pages": [
		// 主要页面
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "嘉依打卡",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white",
				"backgroundColor": "#FFF0F5"
			}
		},
		{
			"path": "pages/water/water",
			"style": {
				"navigationBarTitleText": "喝水打卡",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/video/video",
			"style": {
				"navigationBarTitleText": "视频分享",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/couple/couple",
			"style": {
				"navigationBarTitleText": "情侣互动",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/profile/profile",
			"style": {
				"navigationBarTitleText": "个人中心",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},

		// 认证相关页面
		{
			"path": "pages/auth/login",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/auth/register",
			"style": {
				"navigationBarTitleText": "注册",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},

		// 功能页面
		{
			"path": "pages/achievement/achievement",
			"style": {
				"navigationBarTitleText": "成就中心",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/challenge/challenge",
			"style": {
				"navigationBarTitleText": "挑战任务",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/settings/settings",
			"style": {
				"navigationBarTitleText": "设置",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/statistics/statistics",
			"style": {
				"navigationBarTitleText": "数据统计",
				"navigationBarBackgroundColor": "#FFB6C1",
				"navigationBarTextStyle": "white"
			}
		}
	],

	"tabBar": {
		"color": "#999999",
		"selectedColor": "#FF69B4",
		"backgroundColor": "#FFFFFF",
		"borderStyle": "white",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/tabbar/home.png",
				"selectedIconPath": "static/tabbar/home-active.png"
			},
			{
				"pagePath": "pages/water/water",
				"text": "打卡",
				"iconPath": "static/tabbar/water.png",
				"selectedIconPath": "static/tabbar/water-active.png"
			},
			{
				"pagePath": "pages/video/video",
				"text": "视频",
				"iconPath": "static/tabbar/video.png",
				"selectedIconPath": "static/tabbar/video-active.png"
			},
			{
				"pagePath": "pages/couple/couple",
				"text": "情侣",
				"iconPath": "static/tabbar/couple.png",
				"selectedIconPath": "static/tabbar/couple-active.png"
			},
			{
				"pagePath": "pages/profile/profile",
				"text": "我的",
				"iconPath": "static/tabbar/profile.png",
				"selectedIconPath": "static/tabbar/profile-active.png"
			}
		]
	},

	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "嘉依打卡",
		"navigationBarBackgroundColor": "#FFB6C1",
		"backgroundColor": "#FFF0F5",
		"backgroundTextStyle": "light",
		"enablePullDownRefresh": false
	}
}
