-- =============================================
-- 嘉依打卡：元气水杯与Ta的约定 - 数据库设计
-- 版本：V1.0
-- 日期：2025年7月30日
-- 说明：基于mysql_db.mermaid设计，参考若依框架风格
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 用户相关表
-- =============================================

-- ----------------------------
-- 用户表 - 存储用户基本信息
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` varchar(32) NOT NULL COMMENT '用户ID，主键',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号码，用于登录和验证',
  `nickname` varchar(50) NOT NULL COMMENT '用户昵称，显示名称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL地址',
  `gender` enum('male','female','unknown') DEFAULT 'unknown' COMMENT '性别：male-男性，female-女性，unknown-未知',
  `birthday` date DEFAULT NULL COMMENT '生日日期',
  `bio` varchar(200) DEFAULT NULL COMMENT '个性签名，用户自我介绍',
  `couple_id` varchar(32) DEFAULT NULL COMMENT '情侣关系ID，关联couples表',
  `password` varchar(100) DEFAULT NULL COMMENT '登录密码，加密存储',
  `salt` varchar(50) DEFAULT NULL COMMENT '密码盐值，用于加密',
  `status` char(1) DEFAULT '0' COMMENT '账号状态：0-正常，1-停用，2-删除',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  KEY `idx_couple_id` (`couple_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- ----------------------------
-- 情侣关系表 - 存储情侣绑定关系
-- ----------------------------
DROP TABLE IF EXISTS `couples`;
CREATE TABLE `couples` (
  `id` varchar(32) NOT NULL COMMENT '情侣关系ID，主键',
  `invite_code` varchar(20) NOT NULL COMMENT '邀请码，用于情侣绑定',
  `user1_id` varchar(32) NOT NULL COMMENT '用户1的ID，发起绑定的用户',
  `user2_id` varchar(32) DEFAULT NULL COMMENT '用户2的ID，被邀请的用户',
  `bound_at` datetime DEFAULT NULL COMMENT '绑定成功时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '关系状态：1-活跃，0-已解绑',
  `anniversary_date` date DEFAULT NULL COMMENT '纪念日日期，可自定义设置',
  `couple_name` varchar(100) DEFAULT NULL COMMENT '情侣昵称，如"小明&小红"',
  `total_days` int DEFAULT 0 COMMENT '在一起总天数，自动计算',
  `status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-停用，2-删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  UNIQUE KEY `uk_user1_user2` (`user1_id`, `user2_id`),
  KEY `idx_user1_id` (`user1_id`),
  KEY `idx_user2_id` (`user2_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='情侣关系表';

-- 添加外键约束
ALTER TABLE `users` ADD CONSTRAINT `fk_users_couple_id` FOREIGN KEY (`couple_id`) REFERENCES `couples` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `couples` ADD CONSTRAINT `fk_couples_user1_id` FOREIGN KEY (`user1_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `couples` ADD CONSTRAINT `fk_couples_user2_id` FOREIGN KEY (`user2_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================
-- 喝水功能相关表
-- =============================================

-- ----------------------------
-- 喝水记录表 - 存储用户每次喝水记录
-- ----------------------------
DROP TABLE IF EXISTS `water_records`;
CREATE TABLE `water_records` (
  `id` varchar(32) NOT NULL COMMENT '记录ID，主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID，关联users表',
  `amount_ml` int NOT NULL COMMENT '喝水量，单位毫升(ml)',
  `recorded_at` datetime NOT NULL COMMENT '记录时间，用户打卡的具体时间',
  `cup_type` varchar(50) DEFAULT 'default' COMMENT '杯子类型：default-默认，small-小杯，medium-中杯，large-大杯',
  `cup_size_ml` int DEFAULT 200 COMMENT '杯子容量，单位毫升，用于快捷打卡',
  `note` varchar(200) DEFAULT NULL COMMENT '喝水备注，用户可添加心情或说明',
  `location` varchar(100) DEFAULT NULL COMMENT '打卡地点，可选记录',
  `temperature` enum('hot','warm','cold','ice') DEFAULT NULL COMMENT '水温：hot-热水，warm-温水，cold-凉水，ice-冰水',
  `status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-已删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_recorded_at` (`recorded_at`),
  KEY `idx_user_date` (`user_id`, `recorded_at`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='喝水记录表';

-- ----------------------------
-- 喝水目标表 - 存储用户每日喝水目标
-- ----------------------------
DROP TABLE IF EXISTS `water_goals`;
CREATE TABLE `water_goals` (
  `id` varchar(32) NOT NULL COMMENT '目标ID，主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID，关联users表',
  `daily_goal_ml` int NOT NULL DEFAULT 2000 COMMENT '每日目标喝水量，单位毫升，默认2000ml',
  `goal_date` date NOT NULL COMMENT '目标日期，每天一条记录',
  `actual_amount_ml` int DEFAULT 0 COMMENT '实际完成量，单位毫升，实时更新',
  `is_achieved` tinyint(1) DEFAULT 0 COMMENT '是否达成目标：1-已达成，0-未达成',
  `achievement_time` datetime DEFAULT NULL COMMENT '达成目标的时间',
  `completion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '完成率，百分比，如85.50表示85.5%',
  `status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-已删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `goal_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goal_date` (`goal_date`),
  KEY `idx_is_achieved` (`is_achieved`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='喝水目标表';

-- ----------------------------
-- 喝水提醒表 - 存储用户个性化提醒设置
-- ----------------------------
DROP TABLE IF EXISTS `reminders`;
CREATE TABLE `reminders` (
  `id` varchar(32) NOT NULL COMMENT '提醒ID，主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID，关联users表',
  `reminder_time` time NOT NULL COMMENT '提醒时间，如09:00、14:30',
  `message` varchar(200) DEFAULT '该喝水啦！' COMMENT '提醒消息内容，支持个性化定制',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
  `sound_type` varchar(50) DEFAULT 'default' COMMENT '提醒音效类型：default-默认，bubble-泡泡音，sweet-甜美音',
  `repeat_days` varchar(20) DEFAULT '1,2,3,4,5,6,7' COMMENT '重复日期，1-7表示周一到周日，逗号分隔',
  `snooze_minutes` int DEFAULT 10 COMMENT '延迟提醒分钟数，默认10分钟',
  `priority` int DEFAULT 1 COMMENT '提醒优先级：1-低，2-中，3-高',
  `last_triggered` datetime DEFAULT NULL COMMENT '最后触发时间',
  `trigger_count` int DEFAULT 0 COMMENT '触发次数统计',
  `status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-已删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_reminder_time` (`reminder_time`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_user_active` (`user_id`, `is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='喝水提醒表';

-- 添加喝水功能相关表的外键约束
ALTER TABLE `water_records` ADD CONSTRAINT `fk_water_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `water_goals` ADD CONSTRAINT `fk_water_goals_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `reminders` ADD CONSTRAINT `fk_reminders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================
-- 视频功能相关表
-- =============================================

-- ----------------------------
-- 视频表 - 存储用户上传的喝水视频
-- ----------------------------
DROP TABLE IF EXISTS `videos`;
CREATE TABLE `videos` (
  `id` varchar(32) NOT NULL COMMENT '视频ID，主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID，关联users表',
  `video_url` varchar(500) NOT NULL COMMENT '视频文件URL地址',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '视频缩略图URL地址',
  `description` varchar(500) DEFAULT NULL COMMENT '视频描述，用户添加的文字说明',
  `visibility` enum('private','couple','public') DEFAULT 'couple' COMMENT '可见范围：private-仅自己，couple-情侣可见，public-公开',
  `duration_seconds` int DEFAULT 0 COMMENT '视频时长，单位秒',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小，单位字节',
  `width` int DEFAULT 0 COMMENT '视频宽度，像素',
  `height` int DEFAULT 0 COMMENT '视频高度，像素',
  `format` varchar(20) DEFAULT 'mp4' COMMENT '视频格式：mp4、mov等',
  `filter_name` varchar(50) DEFAULT NULL COMMENT '使用的滤镜名称',
  `music_name` varchar(100) DEFAULT NULL COMMENT '背景音乐名称',
  `tags` varchar(200) DEFAULT NULL COMMENT '视频标签，逗号分隔',
  `like_count` int DEFAULT 0 COMMENT '点赞数量',
  `comment_count` int DEFAULT 0 COMMENT '评论数量',
  `view_count` int DEFAULT 0 COMMENT '播放次数',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否精选：1-是，0-否',
  `status` char(1) DEFAULT '0' COMMENT '视频状态：0-正常，1-审核中，2-已删除，3-违规',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_visibility` (`visibility`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_time` (`user_id`, `create_time`),
  KEY `idx_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频表';

-- ----------------------------
-- 视频互动表 - 存储视频的点赞、评论等互动信息
-- ----------------------------
DROP TABLE IF EXISTS `video_interactions`;
CREATE TABLE `video_interactions` (
  `id` varchar(32) NOT NULL COMMENT '互动ID，主键',
  `video_id` varchar(32) NOT NULL COMMENT '视频ID，关联videos表',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID，关联users表',
  `type` enum('like','comment','share','report') NOT NULL COMMENT '互动类型：like-点赞，comment-评论，share-分享，report-举报',
  `comment_text` text DEFAULT NULL COMMENT '评论内容，当type为comment时使用',
  `emoji_type` varchar(20) DEFAULT NULL COMMENT '表情类型，如heart、smile、love等',
  `reply_to_id` varchar(32) DEFAULT NULL COMMENT '回复的评论ID，用于评论回复功能',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否已删除：1-已删除，0-正常',
  `ip_address` varchar(128) DEFAULT NULL COMMENT '操作IP地址',
  `device_info` varchar(200) DEFAULT NULL COMMENT '设备信息',
  `status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-已删除，2-违规',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_video_user_like` (`video_id`, `user_id`, `type`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_reply_to_id` (`reply_to_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频互动表';

-- 添加视频功能相关表的外键约束
ALTER TABLE `videos` ADD CONSTRAINT `fk_videos_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `video_interactions` ADD CONSTRAINT `fk_video_interactions_video_id` FOREIGN KEY (`video_id`) REFERENCES `videos` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `video_interactions` ADD CONSTRAINT `fk_video_interactions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `video_interactions` ADD CONSTRAINT `fk_video_interactions_reply_to_id` FOREIGN KEY (`reply_to_id`) REFERENCES `video_interactions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================
-- 游戏化功能相关表
-- =============================================

-- ----------------------------
-- 成就表 - 存储系统定义的成就类型
-- ----------------------------
DROP TABLE IF EXISTS `achievements`;
CREATE TABLE `achievements` (
  `id` varchar(32) NOT NULL COMMENT '成就ID，主键',
  `name` varchar(100) NOT NULL COMMENT '成就名称，如"初次打卡"、"连续7天"',
  `description` varchar(500) NOT NULL COMMENT '成就描述，详细说明获得条件',
  `icon_url` varchar(500) DEFAULT NULL COMMENT '成就图标URL地址',
  `type` enum('personal','couple','system') DEFAULT 'personal' COMMENT '成就类型：personal-个人成就，couple-情侣成就，system-系统成就',
  `category` varchar(50) DEFAULT 'water' COMMENT '成就分类：water-喝水相关，video-视频相关，social-社交相关',
  `requirements` json DEFAULT NULL COMMENT '获得条件，JSON格式存储复杂条件',
  `points` int DEFAULT 0 COMMENT '成就积分，用于排行榜等功能',
  `rarity` enum('common','rare','epic','legendary') DEFAULT 'common' COMMENT '稀有度：common-普通，rare-稀有，epic-史诗，legendary-传说',
  `is_hidden` tinyint(1) DEFAULT 0 COMMENT '是否隐藏成就：1-隐藏，0-显示',
  `unlock_order` int DEFAULT 0 COMMENT '解锁顺序，用于成就链',
  `prerequisite_id` varchar(32) DEFAULT NULL COMMENT '前置成就ID，需要先完成的成就',
  `reward_type` varchar(50) DEFAULT NULL COMMENT '奖励类型：theme-主题，sticker-贴纸，title-称号',
  `reward_value` varchar(200) DEFAULT NULL COMMENT '奖励内容，具体的奖励物品',
  `status` char(1) DEFAULT '0' COMMENT '成就状态：0-正常，1-已下线，2-已删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_type` (`type`),
  KEY `idx_category` (`category`),
  KEY `idx_rarity` (`rarity`),
  KEY `idx_status` (`status`),
  KEY `idx_prerequisite_id` (`prerequisite_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就表';

-- ----------------------------
-- 用户成就表 - 存储用户获得的成就记录
-- ----------------------------
DROP TABLE IF EXISTS `user_achievements`;
CREATE TABLE `user_achievements` (
  `id` varchar(32) NOT NULL COMMENT '记录ID，主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID，关联users表',
  `achievement_id` varchar(32) NOT NULL COMMENT '成就ID，关联achievements表',
  `unlocked_at` datetime NOT NULL COMMENT '解锁时间',
  `progress` decimal(5,2) DEFAULT 100.00 COMMENT '完成进度，百分比，100.00表示已完成',
  `current_value` int DEFAULT 0 COMMENT '当前数值，如连续打卡天数',
  `target_value` int DEFAULT 0 COMMENT '目标数值，如需要连续打卡7天',
  `is_notified` tinyint(1) DEFAULT 0 COMMENT '是否已通知：1-已通知，0-未通知',
  `unlock_source` varchar(50) DEFAULT 'auto' COMMENT '解锁来源：auto-自动解锁，manual-手动授予',
  `status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-已删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_achievement` (`user_id`, `achievement_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_achievement_id` (`achievement_id`),
  KEY `idx_unlocked_at` (`unlocked_at`),
  KEY `idx_progress` (`progress`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户成就表';

-- ----------------------------
-- 挑战表 - 存储情侣共同参与的挑战活动
-- ----------------------------
DROP TABLE IF EXISTS `challenges`;
CREATE TABLE `challenges` (
  `id` varchar(32) NOT NULL COMMENT '挑战ID，主键',
  `couple_id` varchar(32) NOT NULL COMMENT '情侣ID，关联couples表',
  `name` varchar(100) NOT NULL COMMENT '挑战名称，如"7天喝水挑战"',
  `description` varchar(500) DEFAULT NULL COMMENT '挑战描述，详细说明挑战内容和规则',
  `type` enum('water','video','interaction','custom') DEFAULT 'water' COMMENT '挑战类型：water-喝水挑战，video-视频挑战，interaction-互动挑战，custom-自定义',
  `duration_days` int NOT NULL DEFAULT 7 COMMENT '挑战持续天数',
  `requirements` json DEFAULT NULL COMMENT '挑战要求，JSON格式存储复杂条件',
  `start_date` date NOT NULL COMMENT '挑战开始日期',
  `end_date` date NOT NULL COMMENT '挑战结束日期',
  `status` enum('pending','active','completed','failed','cancelled') DEFAULT 'pending' COMMENT '挑战状态：pending-待开始，active-进行中，completed-已完成，failed-已失败，cancelled-已取消',
  `user1_progress` decimal(5,2) DEFAULT 0.00 COMMENT '用户1的完成进度，百分比',
  `user2_progress` decimal(5,2) DEFAULT 0.00 COMMENT '用户2的完成进度，百分比',
  `total_progress` decimal(5,2) DEFAULT 0.00 COMMENT '总体完成进度，百分比',
  `reward_type` varchar(50) DEFAULT NULL COMMENT '奖励类型：achievement-成就，theme-主题，title-称号',
  `reward_value` varchar(200) DEFAULT NULL COMMENT '奖励内容',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_by_user` varchar(32) NOT NULL COMMENT '创建挑战的用户ID',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开：1-公开，0-私有',
  `difficulty` enum('easy','medium','hard','expert') DEFAULT 'medium' COMMENT '难度等级：easy-简单，medium-中等，hard-困难，expert-专家',
  `record_status` char(1) DEFAULT '0' COMMENT '记录状态：0-正常，1-已删除',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_couple_id` (`couple_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_type` (`type`),
  KEY `idx_created_by_user` (`created_by_user`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='挑战表';

-- =============================================
-- 系统功能相关表
-- =============================================

-- ----------------------------
-- 通知表 - 存储系统消息和用户通知
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` varchar(32) NOT NULL COMMENT '通知ID，主键',
  `user_id` varchar(32) NOT NULL COMMENT '接收用户ID，关联users表',
  `type` enum('system','achievement','reminder','interaction','challenge','couple') NOT NULL COMMENT '通知类型：system-系统通知，achievement-成就通知，reminder-提醒通知，interaction-互动通知，challenge-挑战通知，couple-情侣通知',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `message` text NOT NULL COMMENT '通知内容',
  `data` json DEFAULT NULL COMMENT '附加数据，JSON格式存储相关信息',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读：1-已读，0-未读',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal' COMMENT '优先级：low-低，normal-普通，high-高，urgent-紧急',
  `action_type` varchar(50) DEFAULT NULL COMMENT '操作类型：click-点击，redirect-跳转，none-无操作',
  `action_url` varchar(500) DEFAULT NULL COMMENT '操作链接，点击通知后跳转的页面',
  `sender_id` varchar(32) DEFAULT NULL COMMENT '发送者ID，用户间通知时使用',
  `related_id` varchar(32) DEFAULT NULL COMMENT '关联对象ID，如成就ID、视频ID等',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联对象类型：achievement、video、challenge等',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间，过期后自动删除',
  `is_push` tinyint(1) DEFAULT 1 COMMENT '是否推送：1-推送，0-不推送',
  `push_at` datetime DEFAULT NULL COMMENT '推送时间',
  `device_tokens` text DEFAULT NULL COMMENT '设备推送令牌，多个用逗号分隔',
  `status` char(1) DEFAULT '0' COMMENT '通知状态：0-正常，1-已删除，2-已过期',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_priority` (`priority`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_related_id` (`related_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_read` (`user_id`, `is_read`),
  KEY `idx_expire_at` (`expire_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- =============================================
-- 添加游戏化功能和系统功能相关表的外键约束
-- =============================================

-- 成就相关外键约束
ALTER TABLE `achievements` ADD CONSTRAINT `fk_achievements_prerequisite_id` FOREIGN KEY (`prerequisite_id`) REFERENCES `achievements` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `user_achievements` ADD CONSTRAINT `fk_user_achievements_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `user_achievements` ADD CONSTRAINT `fk_user_achievements_achievement_id` FOREIGN KEY (`achievement_id`) REFERENCES `achievements` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 挑战相关外键约束
ALTER TABLE `challenges` ADD CONSTRAINT `fk_challenges_couple_id` FOREIGN KEY (`couple_id`) REFERENCES `couples` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `challenges` ADD CONSTRAINT `fk_challenges_created_by_user` FOREIGN KEY (`created_by_user`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 通知相关外键约束
ALTER TABLE `notifications` ADD CONSTRAINT `fk_notifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `notifications` ADD CONSTRAINT `fk_notifications_sender_id` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- =============================================
-- 创建索引优化查询性能
-- =============================================

-- 用户表复合索引
CREATE INDEX `idx_users_phone_status` ON `users` (`phone_number`, `status`);
CREATE INDEX `idx_users_couple_status` ON `users` (`couple_id`, `status`);

-- 喝水记录表复合索引
CREATE INDEX `idx_water_records_user_date` ON `water_records` (`user_id`, DATE(`recorded_at`));
CREATE INDEX `idx_water_records_date_status` ON `water_records` (DATE(`recorded_at`), `status`);

-- 视频表复合索引
CREATE INDEX `idx_videos_user_visibility` ON `videos` (`user_id`, `visibility`, `status`);
CREATE INDEX `idx_videos_status_featured` ON `videos` (`status`, `is_featured`);

-- 通知表复合索引
CREATE INDEX `idx_notifications_user_type_read` ON `notifications` (`user_id`, `type`, `is_read`);

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入默认成就数据
INSERT INTO `achievements` (`id`, `name`, `description`, `type`, `category`, `requirements`, `points`, `rarity`, `create_by`) VALUES
('ach_first_checkin', '初次打卡', '完成第一次喝水打卡', 'personal', 'water', '{"water_records": 1}', 10, 'common', 'system'),
('ach_week_streak', '坚持一周', '连续7天完成喝水目标', 'personal', 'water', '{"consecutive_days": 7}', 50, 'rare', 'system'),
('ach_month_streak', '坚持一月', '连续30天完成喝水目标', 'personal', 'water', '{"consecutive_days": 30}', 200, 'epic', 'system'),
('ach_first_video', '首个视频', '上传第一个喝水视频', 'personal', 'video', '{"videos": 1}', 20, 'common', 'system'),
('ach_couple_bind', '甜蜜绑定', '成功绑定情侣关系', 'couple', 'social', '{"couple_bound": true}', 30, 'common', 'system');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 数据库设计完成
-- 版本：V1.0
-- 总表数：11张
-- 说明：所有表均包含若依风格的通用字段和详细注释
-- =============================================
