package com.maike.api.common.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * 密码工具类
 * 提供密码加密、验证、生成等功能
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
public class PasswordUtils {

    /**
     * 密码最小长度
     */
    private static final int MIN_PASSWORD_LENGTH = 6;

    /**
     * 密码最大长度
     */
    private static final int MAX_PASSWORD_LENGTH = 20;

    /**
     * 密码强度正则表达式（至少包含数字和字母）
     */
    private static final String PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-zA-Z]).{6,20}$";

    /**
     * 编译后的密码正则表达式
     */
    private static final Pattern PATTERN = Pattern.compile(PASSWORD_PATTERN);

    /**
     * BCrypt加密轮数
     */
    private static final int BCRYPT_ROUNDS = 12;

    /**
     * 私有构造方法，防止实例化
     */
    private PasswordUtils() {
    }

    /**
     * 加密密码
     * 使用BCrypt算法对密码进行加密
     * 
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public static String encryptPassword(String rawPassword) {
        if (StrUtil.isBlank(rawPassword)) {
            throw new IllegalArgumentException("密码不能为空");
        }
        return BCrypt.hashpw(rawPassword, BCrypt.gensalt(BCRYPT_ROUNDS));
    }

    /**
     * 验证密码
     * 验证原始密码与加密密码是否匹配
     * 
     * @param rawPassword     原始密码
     * @param encodedPassword 加密后的密码
     * @return true-匹配，false-不匹配
     */
    public static boolean verifyPassword(String rawPassword, String encodedPassword) {
        if (StrUtil.isBlank(rawPassword) || StrUtil.isBlank(encodedPassword)) {
            return false;
        }
        try {
            return BCrypt.checkpw(rawPassword, encodedPassword);
        } catch (Exception e) {
            log.error("密码验证失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证密码格式
     * 检查密码是否符合安全要求
     * 
     * @param password 密码
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidPassword(String password) {
        if (StrUtil.isBlank(password)) {
            return false;
        }
        
        // 检查长度
        if (password.length() < MIN_PASSWORD_LENGTH || password.length() > MAX_PASSWORD_LENGTH) {
            return false;
        }
        
        // 检查是否包含数字和字母
        return PATTERN.matcher(password).matches();
    }

    /**
     * 生成随机密码
     * 生成指定长度的随机密码，包含数字和字母
     * 
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < MIN_PASSWORD_LENGTH || length > MAX_PASSWORD_LENGTH) {
            throw new IllegalArgumentException("密码长度必须在" + MIN_PASSWORD_LENGTH + "-" + MAX_PASSWORD_LENGTH + "之间");
        }
        
        // 确保至少包含一个数字和一个字母
        StringBuilder password = new StringBuilder();
        
        // 添加至少一个数字
        password.append(RandomUtil.randomNumbers(1));
        
        // 添加至少一个字母
        password.append(RandomUtil.randomString("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 1));
        
        // 填充剩余长度
        String chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        for (int i = 2; i < length; i++) {
            password.append(RandomUtil.randomString(chars, 1));
        }
        
        // 打乱字符顺序
        return RandomUtil.randomString(password.toString(), length);
    }

    /**
     * 生成随机密码（默认8位）
     * 
     * @return 随机密码
     */
    public static String generateRandomPassword() {
        return generateRandomPassword(8);
    }

    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 密码强度等级（1-弱，2-中，3-强）
     */
    public static int getPasswordStrength(String password) {
        if (StrUtil.isBlank(password)) {
            return 0;
        }
        
        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) {
            score++;
        }
        
        // 包含数字
        if (password.matches(".*[0-9].*")) {
            score++;
        }
        
        // 包含小写字母
        if (password.matches(".*[a-z].*")) {
            score++;
        }
        
        // 包含大写字母
        if (password.matches(".*[A-Z].*")) {
            score++;
        }
        
        // 包含特殊字符
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            score++;
        }
        
        // 根据得分返回强度等级
        if (score <= 2) {
            return 1; // 弱
        } else if (score <= 3) {
            return 2; // 中
        } else {
            return 3; // 强
        }
    }

    /**
     * 获取密码强度描述
     * 
     * @param password 密码
     * @return 密码强度描述
     */
    public static String getPasswordStrengthDesc(String password) {
        int strength = getPasswordStrength(password);
        switch (strength) {
            case 0:
                return "无效";
            case 1:
                return "弱";
            case 2:
                return "中";
            case 3:
                return "强";
            default:
                return "未知";
        }
    }

    /**
     * 生成盐值
     * 
     * @return 盐值
     */
    public static String generateSalt() {
        return RandomUtil.randomString(32);
    }

    /**
     * 使用盐值加密密码
     * 
     * @param rawPassword 原始密码
     * @param salt        盐值
     * @return 加密后的密码
     */
    public static String encryptPasswordWithSalt(String rawPassword, String salt) {
        if (StrUtil.isBlank(rawPassword) || StrUtil.isBlank(salt)) {
            throw new IllegalArgumentException("密码和盐值不能为空");
        }
        return BCrypt.hashpw(rawPassword + salt, BCrypt.gensalt(BCRYPT_ROUNDS));
    }

    /**
     * 验证带盐值的密码
     * 
     * @param rawPassword     原始密码
     * @param salt            盐值
     * @param encodedPassword 加密后的密码
     * @return true-匹配，false-不匹配
     */
    public static boolean verifyPasswordWithSalt(String rawPassword, String salt, String encodedPassword) {
        if (StrUtil.isBlank(rawPassword) || StrUtil.isBlank(salt) || StrUtil.isBlank(encodedPassword)) {
            return false;
        }
        try {
            return BCrypt.checkpw(rawPassword + salt, encodedPassword);
        } catch (Exception e) {
            log.error("密码验证失败：{}", e.getMessage());
            return false;
        }
    }
}
