/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 - 粉嫩可爱主题 */
$uni-color-primary: #FF69B4; // 热粉色，主要按钮和强调色
$uni-color-primary-light: #FFB6C1; // 浅粉色，导航栏背景
$uni-color-primary-lighter: #FFC0CB; // 更浅粉色，卡片背景
$uni-color-primary-lightest: #FFF0F5; // 最浅粉色，页面背景
$uni-color-success: #98FB98; // 浅绿色，成功状态
$uni-color-warning: #FFE4B5; // 鹿皮色，警告状态
$uni-color-error: #FFA07A; // 浅鲑鱼色，错误状态
$uni-color-secondary: #87CEEB; // 天蓝色，辅助色
$uni-color-accent: #FFE4E1; // 薄雾玫瑰色，装饰色

/* 文字基本颜色 */
$uni-text-color: #333; // 基本色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 - 粉嫩可爱主题 */
$uni-bg-color: #FFFFFF; // 主背景色
$uni-bg-color-page: #FFF0F5; // 页面背景色，薰衣草腮红
$uni-bg-color-card: #FFFFFF; // 卡片背景色
$uni-bg-color-grey: #F8F8FF; // 幽灵白
$uni-bg-color-hover: #FFE4E1; // 点击状态背景色
$uni-bg-color-mask: rgba(255, 182, 193, 0.6); // 遮罩颜色，半透明粉色

/* 边框颜色 - 粉嫩可爱主题 */
$uni-border-color: #FFE4E1; // 主边框颜色
$uni-border-color-light: #FFF0F5; // 浅边框颜色
$uni-border-color-dark: #FFB6C1; // 深边框颜色

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16;

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555; // 二级标题颜色
$uni-font-size-subtitle: 18px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;