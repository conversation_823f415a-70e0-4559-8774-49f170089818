package com.maike.api.water.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 提醒响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ReminderVO {

    /**
     * 提醒ID
     */
    private String id;

    /**
     * 提醒时间
     */
    private LocalTime reminderTime;

    /**
     * 提醒消息内容
     */
    private String message;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 提醒音效类型
     */
    private String soundType;

    /**
     * 音效类型显示文本
     */
    private String soundTypeText;

    /**
     * 重复日期列表
     */
    private List<Integer> repeatDays;

    /**
     * 延迟提醒分钟数
     */
    private Integer snoozeMinutes;

    /**
     * 提醒优先级
     */
    private Integer priority;

    /**
     * 优先级显示文本
     */
    private String priorityText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
