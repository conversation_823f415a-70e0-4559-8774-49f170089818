package com.maike.api.video.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.maike.api.video.pojo.entity.Video;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface VideoMapper extends BaseMapper<Video> {

    /**
     * 分页查询用户视频列表
     * 
     * @param page       分页参数
     * @param userId     用户ID
     * @param visibility 可见范围（可选）
     * @return 分页结果
     */
    IPage<Video> selectUserVideosPage(Page<Video> page, @Param("userId") String userId, @Param("visibility") String visibility);

    /**
     * 分页查询情侣视频列表
     * 
     * @param page    分页参数
     * @param user1Id 用户1ID
     * @param user2Id 用户2ID
     * @return 分页结果
     */
    IPage<Video> selectCoupleVideosPage(Page<Video> page, @Param("user1Id") String user1Id, @Param("user2Id") String user2Id);

    /**
     * 分页查询公开视频列表
     * 
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<Video> selectPublicVideosPage(Page<Video> page);

    /**
     * 分页查询精选视频列表
     * 
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<Video> selectFeaturedVideosPage(Page<Video> page);

    /**
     * 查询用户最近的视频
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 视频列表
     */
    List<Video> selectUserRecentVideos(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 查询情侣最近的视频
     * 
     * @param user1Id 用户1ID
     * @param user2Id 用户2ID
     * @param limit   限制数量
     * @return 视频列表
     */
    List<Video> selectCoupleRecentVideos(@Param("user1Id") String user1Id, @Param("user2Id") String user2Id, @Param("limit") Integer limit);

    /**
     * 更新视频点赞数
     * 
     * @param videoId   视频ID
     * @param increment 增量（正数增加，负数减少）
     * @return 更新行数
     */
    int updateLikeCount(@Param("videoId") String videoId, @Param("increment") Integer increment);

    /**
     * 更新视频评论数
     * 
     * @param videoId   视频ID
     * @param increment 增量（正数增加，负数减少）
     * @return 更新行数
     */
    int updateCommentCount(@Param("videoId") String videoId, @Param("increment") Integer increment);

    /**
     * 更新视频播放次数
     * 
     * @param videoId 视频ID
     * @return 更新行数
     */
    int incrementViewCount(@Param("videoId") String videoId);

    /**
     * 统计用户视频数量
     * 
     * @param userId 用户ID
     * @return 视频数量
     */
    int countUserVideos(@Param("userId") String userId);

    /**
     * 统计情侣视频数量
     * 
     * @param user1Id 用户1ID
     * @param user2Id 用户2ID
     * @return 视频数量
     */
    int countCoupleVideos(@Param("user1Id") String user1Id, @Param("user2Id") String user2Id);

    /**
     * 根据标签搜索视频
     * 
     * @param page 分页参数
     * @param tag  标签
     * @return 分页结果
     */
    IPage<Video> selectVideosByTag(Page<Video> page, @Param("tag") String tag);

    /**
     * 软删除用户视频
     * 
     * @param videoId 视频ID
     * @param userId  用户ID
     * @return 删除行数
     */
    int deleteUserVideo(@Param("videoId") String videoId, @Param("userId") String userId);
}
