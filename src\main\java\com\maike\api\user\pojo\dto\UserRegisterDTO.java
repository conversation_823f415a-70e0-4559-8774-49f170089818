package com.maike.api.user.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class UserRegisterDTO {

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-zA-Z]).{6,20}$", message = "密码必须包含数字和字母")
    private String password;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{4,6}$", message = "验证码格式不正确")
    private String verificationCode;

    /**
     * 用户昵称
     */
    @NotBlank(message = "昵称不能为空")
    @Size(min = 2, max = 20, message = "昵称长度必须在2-20位之间")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,20}$", message = "昵称只能包含中文、字母、数字")
    private String nickname;

    /**
     * 性别：male-男性，female-女性，unknown-未知
     */
    @Pattern(regexp = "^(male|female|unknown)$", message = "性别参数不正确")
    private String gender;

    /**
     * 生日
     */
    private LocalDate birthday;
}
