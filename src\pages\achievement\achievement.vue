<template>
  <view class="achievement-page">
    <!-- 顶部统计 -->
    <view class="stats-header">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ userStats.unlocked_count }}</text>
          <text class="stats-label">已解锁</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ userStats.total_achievements }}</text>
          <text class="stats-label">总成就</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ userStats.total_points }}</text>
          <text class="stats-label">总积分</text>
        </view>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-nav">
      <scroll-view class="nav-scroll" scroll-x>
        <view class="nav-list">
          <view 
            class="nav-item" 
            v-for="category in categories" 
            :key="category.key"
            :class="{ active: currentCategory === category.key }"
            @click="switchCategory(category.key)"
          >
            <image class="nav-icon" :src="category.icon"></image>
            <text class="nav-text">{{ category.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 成就列表 -->
    <view class="achievement-list">
      <view class="achievement-section" v-for="section in achievementSections" :key="section.title">
        <view class="section-header">
          <text class="section-title">{{ section.title }}</text>
          <text class="section-count">{{ section.achievements.length }}</text>
        </view>
        
        <view class="achievement-grid">
          <view 
            class="achievement-item" 
            v-for="achievement in section.achievements" 
            :key="achievement.id"
            :class="{ 
              unlocked: achievement.is_unlocked,
              hidden: achievement.is_hidden && !achievement.is_unlocked
            }"
            @click="showAchievementDetail(achievement)"
          >
            <view class="achievement-icon-container">
              <image 
                class="achievement-icon" 
                :src="achievement.is_unlocked ? achievement.icon_url : '/static/icons/achievement-locked.png'" 
                mode="aspectFit"
              ></image>
              <view class="rarity-badge" :class="achievement.rarity" v-if="achievement.is_unlocked">
                <text>{{ getRarityText(achievement.rarity) }}</text>
              </view>
            </view>
            
            <view class="achievement-info">
              <text class="achievement-name">
                {{ achievement.is_hidden && !achievement.is_unlocked ? '神秘成就' : achievement.name }}
              </text>
              <text class="achievement-desc">
                {{ achievement.is_hidden && !achievement.is_unlocked ? '完成特定条件后解锁' : achievement.description }}
              </text>
              
              <!-- 进度条 -->
              <view class="progress-section" v-if="!achievement.is_unlocked && achievement.progress !== undefined">
                <view class="progress-bar">
                  <view class="progress-fill" :style="{ width: achievement.progress + '%' }"></view>
                </view>
                <text class="progress-text">{{ achievement.current_value }}/{{ achievement.target_value }}</text>
              </view>
              
              <!-- 解锁时间 -->
              <text class="unlock-time" v-if="achievement.is_unlocked">
                {{ formatDate(achievement.unlocked_at) }}
              </text>
            </view>
            
            <!-- 积分 -->
            <view class="achievement-points" v-if="achievement.is_unlocked">
              <text>+{{ achievement.points }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 成就详情弹窗 -->
    <uni-popup ref="achievementDetailPopup" type="center">
      <view class="achievement-detail-popup">
        <view class="detail-header">
          <image 
            class="detail-icon" 
            :src="selectedAchievement.is_unlocked ? selectedAchievement.icon_url : '/static/icons/achievement-locked.png'" 
            mode="aspectFit"
          ></image>
          <view class="rarity-badge large" :class="selectedAchievement.rarity" v-if="selectedAchievement.is_unlocked">
            <text>{{ getRarityText(selectedAchievement.rarity) }}</text>
          </view>
        </view>
        
        <view class="detail-content">
          <text class="detail-name">{{ selectedAchievement.name }}</text>
          <text class="detail-desc">{{ selectedAchievement.description }}</text>
          
          <view class="detail-info">
            <view class="info-item">
              <text class="info-label">类型</text>
              <text class="info-value">{{ getTypeText(selectedAchievement.type) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">分类</text>
              <text class="info-value">{{ getCategoryText(selectedAchievement.category) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">积分</text>
              <text class="info-value">{{ selectedAchievement.points }}</text>
            </view>
            <view class="info-item" v-if="selectedAchievement.is_unlocked">
              <text class="info-label">解锁时间</text>
              <text class="info-value">{{ formatDate(selectedAchievement.unlocked_at) }}</text>
            </view>
          </view>
          
          <!-- 奖励信息 -->
          <view class="reward-section" v-if="selectedAchievement.reward_type">
            <text class="reward-title">奖励</text>
            <view class="reward-item">
              <text class="reward-text">{{ selectedAchievement.reward_value }}</text>
            </view>
          </view>
        </view>
        
        <view class="detail-actions">
          <button class="close-detail-btn" @click="closeAchievementDetail">关闭</button>
          <button 
            class="share-achievement-btn" 
            v-if="selectedAchievement.is_unlocked"
            @click="shareAchievement"
          >
            分享成就
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import request from '@/utils/request'

// 响应式数据
const userStats = ref({
  unlocked_count: 0,
  total_achievements: 0,
  total_points: 0
})

const currentCategory = ref('all')
const achievementList = ref([])
const selectedAchievement = ref({})

// 分类配置
const categories = ref([
  { key: 'all', label: '全部', icon: '/static/icons/all.png' },
  { key: 'water', label: '喝水', icon: '/static/icons/water-drop.png' },
  { key: 'video', label: '视频', icon: '/static/icons/video-camera.png' },
  { key: 'social', label: '社交', icon: '/static/icons/heart.png' },
  { key: 'challenge', label: '挑战', icon: '/static/icons/target.png' }
])

// 弹窗引用
const achievementDetailPopup = ref()

// 计算属性：按分类分组的成就
const achievementSections = computed(() => {
  const filtered = currentCategory.value === 'all' 
    ? achievementList.value 
    : achievementList.value.filter(item => item.category === currentCategory.value)
  
  // 按解锁状态分组
  const unlocked = filtered.filter(item => item.is_unlocked)
  const locked = filtered.filter(item => !item.is_unlocked)
  
  const sections = []
  
  if (unlocked.length > 0) {
    sections.push({
      title: '已解锁',
      achievements: unlocked.sort((a, b) => new Date(b.unlocked_at).getTime() - new Date(a.unlocked_at).getTime())
    })
  }
  
  if (locked.length > 0) {
    sections.push({
      title: '未解锁',
      achievements: locked.sort((a, b) => (b.progress || 0) - (a.progress || 0))
    })
  }
  
  return sections
})

// 页面加载时获取数据
onMounted(() => {
  loadUserStats()
  loadAchievements()
})

// 获取用户统计
const loadUserStats = async () => {
  try {
    const response = await request.get('/achievement/my')
    userStats.value = response.data
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

// 获取成就列表
const loadAchievements = async () => {
  try {
    const response = await request.get('/achievement', {
      category: currentCategory.value === 'all' ? undefined : currentCategory.value
    })
    achievementList.value = response.data.achievements
  } catch (error) {
    console.error('获取成就列表失败:', error)
  }
}

// 切换分类
const switchCategory = (categoryKey: string) => {
  currentCategory.value = categoryKey
  loadAchievements()
}

// 显示成就详情
const showAchievementDetail = (achievement: any) => {
  selectedAchievement.value = achievement
  achievementDetailPopup.value.open()
}

// 关闭成就详情
const closeAchievementDetail = () => {
  achievementDetailPopup.value.close()
}

// 分享成就
const shareAchievement = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '',
    title: '嘉依打卡成就分享',
    summary: `我在嘉依打卡解锁了"${selectedAchievement.value.name}"成就！`,
    imageUrl: selectedAchievement.value.icon_url
  })
}

// 获取稀有度文本
const getRarityText = (rarity: string) => {
  const rarityMap = {
    common: '普通',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说'
  }
  return rarityMap[rarity] || '普通'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap = {
    personal: '个人成就',
    couple: '情侣成就',
    system: '系统成就'
  }
  return typeMap[type] || '个人成就'
}

// 获取分类文本
const getCategoryText = (category: string) => {
  const categoryMap = {
    water: '喝水相关',
    video: '视频相关',
    social: '社交相关',
    challenge: '挑战相关'
  }
  return categoryMap[category] || '其他'
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.achievement-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
}

/* 顶部统计样式 */
.stats-header {
  padding: 32rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.stats-divider {
  width: 1rpx;
  height: 60rpx;
  background: #FFE4E1;
}

/* 分类导航样式 */
.category-nav {
  padding: 0 32rpx 32rpx;
}

.nav-scroll {
  white-space: nowrap;
}

.nav-list {
  display: flex;
  gap: 24rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.1);
}

.nav-item.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

.nav-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
}

.nav-text {
  font-size: 24rpx;
  font-weight: bold;
}

.nav-item:not(.active) .nav-text {
  color: #666;
}

/* 成就列表样式 */
.achievement-list {
  padding: 0 32rpx 32rpx;
}

.achievement-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-count {
  font-size: 24rpx;
  color: #999;
  background: #FFE4E1;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.achievement-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.achievement-item:active {
  transform: scale(0.98);
}

.achievement-item.hidden {
  opacity: 0.6;
}

.achievement-item:not(.unlocked) {
  opacity: 0.7;
}

.achievement-icon-container {
  position: relative;
  width: 80rpx;
  height: 80rpx;
}

.achievement-icon {
  width: 100%;
  height: 100%;
}

.rarity-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 16rpx;
  font-weight: bold;
  color: white;
}

.rarity-badge.common {
  background: #87CEEB;
}

.rarity-badge.rare {
  background: #9370DB;
}

.rarity-badge.epic {
  background: #FF6347;
}

.rarity-badge.legendary {
  background: #FFD700;
  color: #333;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.achievement-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.progress-section {
  margin-bottom: 8rpx;
}

.progress-bar {
  height: 8rpx;
  background: #FFE4E1;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #999;
}

.unlock-time {
  font-size: 20rpx;
  color: #999;
}

.achievement-points {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

/* 成就详情弹窗样式 */
.achievement-detail-popup {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx;
  width: 80vw;
  max-width: 600rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  text-align: center;
}

.detail-header {
  position: relative;
  margin-bottom: 32rpx;
}

.detail-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 16rpx;
}

.rarity-badge.large {
  position: static;
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.detail-content {
  text-align: left;
  margin-bottom: 32rpx;
}

.detail-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  text-align: center;
}

.detail-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
  text-align: center;
}

.detail-info {
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #999;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.reward-section {
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
  border-radius: 16rpx;
  padding: 24rpx;
}

.reward-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 16rpx;
}

.reward-item {
  background: white;
  border-radius: 12rpx;
  padding: 16rpx;
}

.reward-text {
  font-size: 24rpx;
  color: #333;
}

.detail-actions {
  display: flex;
  gap: 24rpx;
}

.close-detail-btn, .share-achievement-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.close-detail-btn {
  background: #F5F5F5;
  color: #666;
}

.share-achievement-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}
</style>
