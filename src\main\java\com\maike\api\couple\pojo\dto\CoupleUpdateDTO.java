package com.maike.api.couple.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 更新情侣信息请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleUpdateDTO {

    /**
     * 情侣昵称
     */
    @Size(min = 2, max = 100, message = "情侣昵称长度必须在2-100位之间")
    private String coupleName;

    /**
     * 纪念日日期
     */
    private LocalDate anniversaryDate;
}
