package com.maike.api.water.pojo.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 设置喝水目标请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class WaterGoalCreateDTO {

    /**
     * 每日目标喝水量，单位毫升
     */
    @NotNull(message = "每日目标喝水量不能为空")
    @Min(value = 500, message = "每日目标喝水量不能小于500ml")
    @Max(value = 10000, message = "每日目标喝水量不能超过10000ml")
    private Integer dailyGoalMl;

    /**
     * 目标日期（可选，默认今天）
     */
    private LocalDate goalDate;
}
