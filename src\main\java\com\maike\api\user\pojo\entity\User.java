package com.maike.api.user.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表：users
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 手机号码，用于登录和验证
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 用户昵称，显示名称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 头像URL地址
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 性别：male-男性，female-女性，unknown-未知
     */
    @TableField("gender")
    private String gender;

    /**
     * 生日日期
     */
    @TableField("birthday")
    private LocalDate birthday;

    /**
     * 个性签名，用户自我介绍
     */
    @TableField("bio")
    private String bio;

    /**
     * 情侣关系ID，关联couples表
     */
    @TableField("couple_id")
    private String coupleId;

    /**
     * 登录密码，加密存储
     */
    @TableField("password")
    private String password;

    /**
     * 密码盐值，用于加密
     */
    @TableField("salt")
    private String salt;

    /**
     * 账号状态：0-正常，1-停用，2-删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "2")
    private String status;

    /**
     * 最后登录IP
     */
    @TableField("login_ip")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @TableField("login_date")
    private LocalDateTime loginDate;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断用户是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断用户是否已停用
     * 
     * @return true-已停用，false-未停用
     */
    public boolean isDisabled() {
        return "1".equals(this.status);
    }

    /**
     * 判断用户是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "2".equals(this.status);
    }

    /**
     * 判断用户是否有情侣关系
     * 
     * @return true-有情侣关系，false-无情侣关系
     */
    public boolean hasCouple() {
        return this.coupleId != null && !this.coupleId.trim().isEmpty();
    }

    /**
     * 判断用户性别是否为男性
     * 
     * @return true-男性，false-非男性
     */
    public boolean isMale() {
        return "male".equals(this.gender);
    }

    /**
     * 判断用户性别是否为女性
     * 
     * @return true-女性，false-非女性
     */
    public boolean isFemale() {
        return "female".equals(this.gender);
    }

    /**
     * 获取性别显示文本
     * 
     * @return 性别显示文本
     */
    public String getGenderText() {
        switch (this.gender) {
            case "male":
                return "男";
            case "female":
                return "女";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusText() {
        switch (this.status) {
            case "0":
                return "正常";
            case "1":
                return "停用";
            case "2":
                return "删除";
            default:
                return "未知";
        }
    }

    /**
     * 脱敏手机号
     * 
     * @return 脱敏后的手机号
     */
    public String getMaskedPhoneNumber() {
        if (this.phoneNumber == null || this.phoneNumber.length() < 11) {
            return this.phoneNumber;
        }
        return this.phoneNumber.substring(0, 3) + "****" + this.phoneNumber.substring(7);
    }
}
