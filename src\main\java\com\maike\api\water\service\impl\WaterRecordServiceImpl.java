package com.maike.api.water.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maike.api.common.exception.BusinessException;
import com.maike.api.common.result.ResultCode;
import com.maike.api.user.service.UserService;
import com.maike.api.water.mapper.WaterRecordMapper;
import com.maike.api.water.pojo.dto.WaterRecordCreateDTO;
import com.maike.api.water.pojo.dto.WaterRecordUpdateDTO;
import com.maike.api.water.pojo.entity.WaterRecord;
import com.maike.api.water.pojo.vo.WaterRecordCreateVO;
import com.maike.api.water.pojo.vo.WaterRecordVO;
import com.maike.api.water.service.WaterGoalService;
import com.maike.api.water.service.WaterRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 喝水记录服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WaterRecordServiceImpl extends ServiceImpl<WaterRecordMapper, WaterRecord> implements WaterRecordService {

    private final WaterRecordMapper waterRecordMapper;
    private final WaterGoalService waterGoalService;
    private final UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WaterRecordCreateVO addWaterRecord(String userId, WaterRecordCreateDTO createDTO) {
        log.info("添加喝水记录，用户ID：{}", userId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 验证喝水量
        if (createDTO.getAmountMl() == null || createDTO.getAmountMl() <= 0) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "喝水量必须大于0");
        }

        // 3. 创建记录
        WaterRecord record = new WaterRecord();
        BeanUtil.copyProperties(createDTO, record);
        record.setUserId(userId);
        
        // 4. 设置记录时间，如果未提供则使用当前时间
        if (record.getRecordedAt() == null) {
            record.setRecordedAt(LocalDateTime.now());
        }
        
        // 5. 设置默认值
        if (StrUtil.isBlank(record.getCupType())) {
            record.setCupType("default");
        }
        
        if (record.getCupSizeMl() == null) {
            // 根据杯子类型设置默认容量
            switch (record.getCupType()) {
                case "small":
                    record.setCupSizeMl(200);
                    break;
                case "medium":
                    record.setCupSizeMl(300);
                    break;
                case "large":
                    record.setCupSizeMl(500);
                    break;
                case "bottle":
                    record.setCupSizeMl(750);
                    break;
                default:
                    record.setCupSizeMl(200);
            }
        }
        
        record.setStatus("0");
        record.setCreateBy(userId);
        record.setUpdateBy(userId);

        // 6. 保存记录
        if (!save(record)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "添加喝水记录失败");
        }

        // 7. 更新当日目标
        LocalDate recordDate = record.getRecordedAt().toLocalDate();
        Integer todayTotal = getUserDailyAmount(userId, recordDate);
        waterGoalService.updateActualAmount(userId, recordDate, todayTotal);
        
        // 8. 检查目标达成状态
        waterGoalService.checkAndUpdateAchievement(userId, recordDate);
        
        // 9. 获取目标信息
        com.maike.api.water.pojo.entity.WaterGoal goal = waterGoalService.getOrCreateUserGoal(userId, recordDate);
        
        // 10. 构建返回结果
        WaterRecordCreateVO createVO = new WaterRecordCreateVO();
        createVO.setRecordId(record.getId());
        createVO.setTodayTotal(todayTotal);
        createVO.setGoalProgress(goal.getCompletionRate());
        createVO.setIsGoalAchieved(goal.getIsAchieved());
        createVO.setRemainingAmount(goal.getRemainingAmount());

        log.info("喝水记录添加成功，记录ID：{}", record.getId());
        return createVO;
    }

    @Override
    public IPage<WaterRecordVO> getUserRecordsPage(String userId, LocalDate date, Integer page, Integer size) {
        log.info("分页查询用户喝水记录，用户ID：{}，日期：{}", userId, date);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        
        if (size == null || size < 1) {
            size = 20;
        }

        // 3. 查询记录
        Page<WaterRecord> pageParam = new Page<>(page, size);
        IPage<WaterRecord> recordPage = waterRecordMapper.selectUserRecordsPage(pageParam, userId, date);

        // 4. 转换为VO
        IPage<WaterRecordVO> voPage = recordPage.convert(record -> {
            WaterRecordVO vo = new WaterRecordVO();
            BeanUtil.copyProperties(record, vo);
            vo.setCupTypeText(record.getCupTypeText());
            vo.setTemperatureText(record.getTemperatureText());
            return vo;
        });

        return voPage;
    }

    @Override
    public List<WaterRecordVO> getUserRecordsByDate(String userId, LocalDate date) {
        log.info("查询用户指定日期的喝水记录，用户ID：{}，日期：{}", userId, date);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询记录
        List<WaterRecord> records = waterRecordMapper.selectUserRecordsByDate(userId, date);

        // 3. 转换为VO
        return records.stream().map(record -> {
            WaterRecordVO vo = new WaterRecordVO();
            BeanUtil.copyProperties(record, vo);
            vo.setCupTypeText(record.getCupTypeText());
            vo.setTemperatureText(record.getTemperatureText());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateWaterRecord(String userId, String recordId, WaterRecordUpdateDTO updateDTO) {
        log.info("更新喝水记录，用户ID：{}，记录ID：{}", userId, recordId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询记录
        WaterRecord record = getById(recordId);
        if (record == null || !record.isNormal() || !userId.equals(record.getUserId())) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "记录不存在或无权限");
        }

        List<String> updatedFields = new ArrayList<>();
        Integer oldAmount = record.getAmountMl();

        // 3. 更新字段
        if (updateDTO.getAmountMl() != null && !updateDTO.getAmountMl().equals(record.getAmountMl())) {
            if (updateDTO.getAmountMl() <= 0) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "喝水量必须大于0");
            }
            record.setAmountMl(updateDTO.getAmountMl());
            updatedFields.add("amountMl");
        }

        if (StrUtil.isNotBlank(updateDTO.getCupType()) && !updateDTO.getCupType().equals(record.getCupType())) {
            record.setCupType(updateDTO.getCupType());
            updatedFields.add("cupType");
        }

        if (StrUtil.isNotBlank(updateDTO.getTemperature()) && !updateDTO.getTemperature().equals(record.getTemperature())) {
            record.setTemperature(updateDTO.getTemperature());
            updatedFields.add("temperature");
        }

        if (updateDTO.getNote() != null && !updateDTO.getNote().equals(record.getNote())) {
            record.setNote(updateDTO.getNote());
            updatedFields.add("note");
        }

        if (StrUtil.isNotBlank(updateDTO.getLocation()) && !updateDTO.getLocation().equals(record.getLocation())) {
            record.setLocation(updateDTO.getLocation());
            updatedFields.add("location");
        }

        if (updateDTO.getRecordedAt() != null && !updateDTO.getRecordedAt().equals(record.getRecordedAt())) {
            record.setRecordedAt(updateDTO.getRecordedAt());
            updatedFields.add("recordedAt");
        }

        // 4. 保存更新
        if (!updatedFields.isEmpty()) {
            record.setUpdateBy(userId);
            if (!updateById(record)) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "更新喝水记录失败");
            }

            // 5. 如果喝水量发生变化，需要更新目标
            if (updatedFields.contains("amountMl") || updatedFields.contains("recordedAt")) {
                LocalDate recordDate = record.getRecordedAt().toLocalDate();
                Integer todayTotal = getUserDailyAmount(userId, recordDate);
                waterGoalService.updateActualAmount(userId, recordDate, todayTotal);
                waterGoalService.checkAndUpdateAchievement(userId, recordDate);

                // 如果记录日期发生变化，还需要更新原日期的目标
                if (updatedFields.contains("recordedAt")) {
                    LocalDate oldDate = LocalDate.now(); // 这里应该从原记录中获取，简化处理
                    Integer oldDateTotal = getUserDailyAmount(userId, oldDate);
                    waterGoalService.updateActualAmount(userId, oldDate, oldDateTotal);
                    waterGoalService.checkAndUpdateAchievement(userId, oldDate);
                }
            }
        }

        log.info("喝水记录更新成功，更新字段：{}", updatedFields);
        return updatedFields;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWaterRecord(String userId, String recordId) {
        log.info("删除喝水记录，用户ID：{}，记录ID：{}", userId, recordId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询记录
        WaterRecord record = getById(recordId);
        if (record == null || !record.isNormal() || !userId.equals(record.getUserId())) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "记录不存在或无权限");
        }

        // 3. 软删除记录
        if (waterRecordMapper.deleteUserRecord(recordId, userId) <= 0) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "删除喝水记录失败");
        }

        // 4. 更新当日目标
        LocalDate recordDate = record.getRecordedAt().toLocalDate();
        Integer todayTotal = getUserDailyAmount(userId, recordDate);
        waterGoalService.updateActualAmount(userId, recordDate, todayTotal);
        waterGoalService.checkAndUpdateAchievement(userId, recordDate);

        log.info("喝水记录删除成功，记录ID：{}", recordId);
    }

    @Override
    public Integer getUserDailyAmount(String userId, LocalDate date) {
        Integer amount = waterRecordMapper.sumUserDailyAmount(userId, date);
        return amount != null ? amount : 0;
    }

    @Override
    public List<WaterRecordVO> getUserRecentRecords(String userId, Integer limit) {
        log.info("查询用户最近的喝水记录，用户ID：{}，限制数量：{}", userId, limit);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 设置默认值
        if (limit == null || limit < 1) {
            limit = 10;
        }

        // 3. 查询记录
        List<WaterRecord> records = waterRecordMapper.selectUserRecentRecords(userId, limit);

        // 4. 转换为VO
        return records.stream().map(record -> {
            WaterRecordVO vo = new WaterRecordVO();
            BeanUtil.copyProperties(record, vo);
            vo.setCupTypeText(record.getCupTypeText());
            vo.setTemperatureText(record.getTemperatureText());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DailyWaterStatVO> getUserWaterStats(String userId, LocalDate startDate, LocalDate endDate) {
        log.info("获取用户喝水统计数据，用户ID：{}，开始日期：{}，结束日期：{}", userId, startDate, endDate);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询统计数据
        List<WaterRecordMapper.DailyWaterStat> stats = waterRecordMapper.selectUserDailyStats(userId, startDate, endDate);

        // 3. 转换为VO
        return stats.stream().map(stat -> {
            DailyWaterStatVO vo = new DailyWaterStatVO();
            vo.setDate(stat.getDate());
            vo.setTotalAmount(stat.getTotalAmount());
            vo.setRecordCount(stat.getRecordCount());
            return vo;
        }).collect(Collectors.toList());
    }
}
