package com.maike.api.common.interceptor;

import cn.hutool.core.util.StrUtil;
import com.maike.api.common.exception.BusinessException;
import com.maike.api.common.result.ResultCode;
import com.maike.api.common.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * JWT拦截器
 * 用于验证JWT令牌的有效性
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtInterceptor implements HandlerInterceptor {

    private final JwtUtils jwtUtils;
    private final StringRedisTemplate redisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求路径
        String requestURI = request.getRequestURI();
        log.debug("JWT拦截器处理请求：{}", requestURI);

        // 从请求头中获取token
        String authHeader = request.getHeader("Authorization");
        String token = jwtUtils.getTokenFromHeader(authHeader);

        if (StrUtil.isBlank(token)) {
            log.warn("请求头中未找到有效的JWT令牌，请求路径：{}", requestURI);
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        // 检查token是否在黑名单中
        String blacklistKey = "jwt_blacklist:" + token;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(blacklistKey))) {
            log.warn("JWT令牌已在黑名单中，请求路径：{}", requestURI);
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        // 验证token有效性
        if (!jwtUtils.validateToken(token)) {
            log.warn("JWT令牌验证失败，请求路径：{}", requestURI);
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        // 从token中获取用户信息
        String userId = jwtUtils.getUserIdFromToken(token);
        if (StrUtil.isBlank(userId)) {
            log.warn("JWT令牌中未找到用户ID，请求路径：{}", requestURI);
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        // 将用户ID存储到请求属性中，供后续使用
        request.setAttribute("currentUserId", userId);

        log.debug("JWT令牌验证成功，用户ID：{}，请求路径：{}", userId, requestURI);
        return true;
    }
}
