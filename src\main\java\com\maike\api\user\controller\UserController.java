package com.maike.api.user.controller;

import com.maike.api.common.result.Result;
import com.maike.api.common.utils.JwtUtils;
import com.maike.api.user.pojo.dto.*;
import com.maike.api.user.pojo.vo.LoginVO;
import com.maike.api.user.pojo.vo.RegisterVO;
import com.maike.api.user.pojo.vo.UserInfoVO;
import com.maike.api.user.service.UserService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户认证控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */

@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Validated
public class UserController {

    private final UserService userService;
    private final JwtUtils jwtUtils;

    /**
     * 用户注册
     * 
     * @param registerDTO 注册信息
     * @param request     HTTP请求
     * @return 注册结果
     */
    @PostMapping
    public Result<RegisterVO> register(@Valid @RequestBody UserRegisterDTO registerDTO, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        RegisterVO registerVO = userService.register(registerDTO, clientIp);
        return Result.success("注册成功", registerVO);
    }

    /**
     * 用户登录
     * 
     * @param loginDTO 登录信息
     * @param request  HTTP请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<LoginVO> login(@Valid @RequestBody UserLoginDTO loginDTO, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        LoginVO loginVO = userService.login(loginDTO, clientIp);
        return Result.success("登录成功", loginVO);
    }

    /**
     * 获取用户信息
     * 
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping
    public Result<UserInfoVO> getUserInfo(HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        UserInfoVO userInfo = userService.getUserInfo(userId);
        return Result.success("获取成功", userInfo);
    }

    /**
     * 更新用户信息
     * 
     * @param updateDTO 更新信息
     * @param request   HTTP请求
     * @return 更新结果
     */
    @PutMapping
    public Result<Map<String, Object>> updateUserInfo(@Valid @RequestBody UserUpdateDTO updateDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        List<String> updatedFields = userService.updateUserInfo(userId, updateDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("updated_fields", updatedFields);
        
        return Result.success("更新成功", result);
    }

    /**
     * 修改密码
     * 
     * @param passwordChangeDTO 密码修改信息
     * @param request           HTTP请求
     * @return 修改结果
     */
    @PutMapping("/password")
    public Result<Void> changePassword(@Valid @RequestBody PasswordChangeDTO passwordChangeDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userService.changePassword(userId, passwordChangeDTO);
        return Result.success("密码修改成功");
    }

    /**
     * 发送验证码
     * 
     * @param sendCodeDTO 发送验证码信息
     * @return 发送结果
     */
    @PostMapping("/sendCode")
    public Result<Map<String, Object>> sendVerificationCode(@Valid @RequestBody SendCodeDTO sendCodeDTO) {
        Integer expiresIn = userService.sendVerificationCode(sendCodeDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("expires_in", expiresIn);
        
        return Result.success("验证码发送成功", result);
    }

    /**
     * 退出登录
     * 
     * @param request HTTP请求
     * @return 退出结果
     */
    @DeleteMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        String token = getTokenFromRequest(request);
        userService.logout(userId, token);
        return Result.success("退出成功");
    }

    /**
     * 删除账户
     * 
     * @param deleteDTO 删除账户信息
     * @param request   HTTP请求
     * @return 删除结果
     */
    @DeleteMapping
    public Result<Void> deleteAccount(@Valid @RequestBody UserDeleteDTO deleteDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        userService.deleteAccount(userId, deleteDTO);
        return Result.success("账户删除成功");
    }

    /**
     * 从请求中获取当前用户ID
     * 
     * @param request HTTP请求
     * @return 用户ID
     */
    private String getCurrentUserId(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        String token = jwtUtils.getTokenFromHeader(authHeader);
        return jwtUtils.getUserIdFromToken(token);
    }

    /**
     * 从请求中获取JWT令牌
     * 
     * @param request HTTP请求
     * @return JWT令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        return jwtUtils.getTokenFromHeader(authHeader);
    }

    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
