package com.maike.api.water.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.maike.api.water.pojo.entity.WaterRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 喝水记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface WaterRecordMapper extends BaseMapper<WaterRecord> {

    /**
     * 分页查询用户喝水记录
     * 
     * @param page   分页参数
     * @param userId 用户ID
     * @param date   查询日期（可选）
     * @return 分页结果
     */
    IPage<WaterRecord> selectUserRecordsPage(Page<WaterRecord> page, @Param("userId") String userId, @Param("date") LocalDate date);

    /**
     * 查询用户指定日期的喝水记录
     * 
     * @param userId 用户ID
     * @param date   查询日期
     * @return 喝水记录列表
     */
    List<WaterRecord> selectUserRecordsByDate(@Param("userId") String userId, @Param("date") LocalDate date);

    /**
     * 计算用户指定日期的总喝水量
     * 
     * @param userId 用户ID
     * @param date   查询日期
     * @return 总喝水量（毫升）
     */
    Integer sumUserDailyAmount(@Param("userId") String userId, @Param("date") LocalDate date);

    /**
     * 查询用户指定时间范围内的喝水记录
     * 
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 喝水记录列表
     */
    List<WaterRecord> selectUserRecordsByTimeRange(@Param("userId") String userId, 
                                                   @Param("startTime") LocalDateTime startTime, 
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户最近的喝水记录
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 喝水记录列表
     */
    List<WaterRecord> selectUserRecentRecords(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 统计用户指定日期范围内每日的喝水量
     * 
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 每日喝水量统计
     */
    List<DailyWaterStat> selectUserDailyStats(@Param("userId") String userId, 
                                              @Param("startDate") LocalDate startDate, 
                                              @Param("endDate") LocalDate endDate);

    /**
     * 删除用户指定记录
     * 
     * @param recordId 记录ID
     * @param userId   用户ID
     * @return 删除行数
     */
    int deleteUserRecord(@Param("recordId") String recordId, @Param("userId") String userId);

    /**
     * 每日喝水量统计内部类
     */
    class DailyWaterStat {
        private LocalDate date;
        private Integer totalAmount;
        private Integer recordCount;

        // getters and setters
        public LocalDate getDate() { return date; }
        public void setDate(LocalDate date) { this.date = date; }
        public Integer getTotalAmount() { return totalAmount; }
        public void setTotalAmount(Integer totalAmount) { this.totalAmount = totalAmount; }
        public Integer getRecordCount() { return recordCount; }
        public void setRecordCount(Integer recordCount) { this.recordCount = recordCount; }
    }
}
