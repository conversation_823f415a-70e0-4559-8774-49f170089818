<template>
  <view class="login-page">
    <!-- 顶部装饰 -->
    <view class="header-decoration">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">嘉依打卡</text>
      <text class="app-slogan">元气水杯与Ta的约定</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-title">
        <text>欢迎回来</text>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/phone.png"></image>
          <input 
            class="form-input" 
            type="number" 
            v-model="loginForm.phone_number" 
            placeholder="请输入手机号"
            maxlength="11"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/password.png"></image>
          <input 
            class="form-input" 
            :type="showPassword ? 'text' : 'password'" 
            v-model="loginForm.password" 
            placeholder="请输入密码"
          />
          <image 
            class="toggle-password" 
            :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
            @click="showPassword = !showPassword"
          ></image>
        </view>
      </view>
      
      <view class="form-actions">
        <text class="forgot-password" @click="goToForgotPassword">忘记密码？</text>
      </view>
      
      <button class="login-btn" @click="handleLogin" :disabled="!canLogin">
        登录
      </button>
      
      <view class="register-link">
        <text>还没有账号？</text>
        <text class="link-text" @click="goToRegister">立即注册</text>
      </view>
    </view>

    <!-- 第三方登录 -->
    <view class="third-party-login">
      <view class="divider">
        <text>或</text>
      </view>
      
      <view class="third-party-buttons">
        <view class="third-party-btn" @click="wechatLogin">
          <image class="third-party-icon" src="/static/icons/wechat.png"></image>
          <text>微信登录</text>
        </view>
      </view>
    </view>

    <!-- 底部协议 -->
    <view class="agreement">
      <text>登录即表示同意</text>
      <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
      <text>和</text>
      <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import request from '@/utils/request'

// 响应式数据
const loginForm = ref({
  phone_number: '',
  password: ''
})

const showPassword = ref(false)

// 计算属性：是否可以登录
const canLogin = computed(() => {
  return loginForm.value.phone_number.length === 11 && 
         loginForm.value.password.length >= 6
})

// 登录处理
const handleLogin = async () => {
  if (!canLogin.value) {
    uni.showToast({
      title: '请检查输入信息',
      icon: 'none'
    })
    return
  }
  
  try {
    uni.showLoading({ title: '登录中...' })
    
    const response = await request.post('/user/login', loginForm.value)
    
    // 保存token和用户信息
    request.setToken(response.data.token)
    uni.setStorageSync('userInfo', response.data.user_info)
    
    uni.hideLoading()
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
    // 跳转到首页
    setTimeout(() => {
      uni.switchTab({ url: '/pages/index/index' })
    }, 1500)
    
  } catch (error) {
    uni.hideLoading()
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'error'
    })
  }
}

// 微信登录
const wechatLogin = () => {
  // #ifdef MP-WEIXIN
  uni.login({
    provider: 'weixin',
    success: (res) => {
      console.log('微信登录成功:', res)
      // 这里需要将code发送到后端进行验证
      // handleWechatLogin(res.code)
    },
    fail: (error) => {
      console.error('微信登录失败:', error)
      uni.showToast({
        title: '微信登录失败',
        icon: 'error'
      })
    }
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '当前环境不支持微信登录',
    icon: 'none'
  })
  // #endif
}

// 页面跳转方法
const goToRegister = () => {
  uni.navigateTo({ url: '/pages/auth/register' })
}

const goToForgotPassword = () => {
  uni.navigateTo({ url: '/pages/auth/forgot-password' })
}

const showUserAgreement = () => {
  uni.navigateTo({ url: '/pages/legal/user-agreement' })
}

const showPrivacyPolicy = () => {
  uni.navigateTo({ url: '/pages/legal/privacy-policy' })
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFF0F5 50%, #FFE4E1 100%);
  padding: 0 48rpx;
  display: flex;
  flex-direction: column;
}

/* 顶部装饰样式 */
.header-decoration {
  text-align: center;
  padding: 120rpx 0 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 16rpx;
}

.app-slogan {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 登录表单样式 */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 16rpx 64rpx rgba(255, 182, 193, 0.2);
}

.form-title {
  text-align: center;
  margin-bottom: 48rpx;
}

.form-title text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form-item {
  margin-bottom: 32rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #FFF0F5;
  border-radius: 48rpx;
  padding: 0 32rpx;
  border: 2rpx solid #FFE4E1;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #FF69B4;
  box-shadow: 0 0 0 4rpx rgba(255, 105, 180, 0.1);
}

.input-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.form-input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
}

.toggle-password {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 48rpx;
}

.forgot-password {
  font-size: 24rpx;
  color: #FF69B4;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
  margin-bottom: 32rpx;
}

.login-btn:disabled {
  background: #E0E0E0;
  color: #999;
  box-shadow: none;
}

.login-btn:active:not(:disabled) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.4);
}

.register-link {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.link-text {
  color: #FF69B4;
  font-weight: bold;
  margin-left: 8rpx;
}

/* 第三方登录样式 */
.third-party-login {
  margin-bottom: 48rpx;
}

.divider {
  text-align: center;
  position: relative;
  margin-bottom: 32rpx;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: rgba(255, 182, 193, 0.3);
}

.divider text {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFF0F5 50%, #FFE4E1 100%);
  padding: 0 24rpx;
  font-size: 24rpx;
  color: #999;
}

.third-party-buttons {
  display: flex;
  justify-content: center;
}

.third-party-btn {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 48rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
}

.third-party-btn:active {
  transform: scale(0.95);
}

.third-party-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.third-party-btn text {
  font-size: 28rpx;
  color: #333;
}

/* 底部协议样式 */
.agreement {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-top: auto;
  padding-bottom: 48rpx;
}

.agreement-link {
  color: #FF69B4;
}
</style>
