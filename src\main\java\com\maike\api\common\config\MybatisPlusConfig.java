package com.maike.api.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus配置类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * 分页插件配置
     *
     * @return MybatisPlusInterceptor
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        paginationInnerInterceptor.setDbType(DbType.MYSQL);
        paginationInnerInterceptor.setMaxLimit(1000L); // 最大分页限制
        paginationInnerInterceptor.setOverflow(false); // 溢出总页数后是否进行处理

        interceptor.addInnerInterceptor(paginationInnerInterceptor);

        return interceptor;
    }

    /**
     * 自动填充处理器
     * 用于自动填充创建时间、更新时间等字段
     */
    @Slf4j
    @Component
    public static class MyMetaObjectHandler implements MetaObjectHandler {

        /**
         * 插入时自动填充
         * 
         * @param metaObject 元对象
         */
        @Override
        public void insertFill(MetaObject metaObject) {
            log.debug("开始插入填充...");
            
            // 自动填充创建时间
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
            
            // 自动填充更新时间
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            
            // 自动填充创建者（如果没有设置的话）
            if (!metaObject.hasGetter("createBy") || metaObject.getValue("createBy") == null) {
                this.strictInsertFill(metaObject, "createBy", String.class, "system");
            }
            
            // 自动填充更新者（如果没有设置的话）
            if (!metaObject.hasGetter("updateBy") || metaObject.getValue("updateBy") == null) {
                this.strictInsertFill(metaObject, "updateBy", String.class, "system");
            }
        }

        /**
         * 更新时自动填充
         * 
         * @param metaObject 元对象
         */
        @Override
        public void updateFill(MetaObject metaObject) {
            log.debug("开始更新填充...");
            
            // 自动填充更新时间
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            
            // 自动填充更新者（如果没有设置的话）
            if (!metaObject.hasGetter("updateBy") || metaObject.getValue("updateBy") == null) {
                this.strictUpdateFill(metaObject, "updateBy", String.class, "system");
            }
        }
    }
}
