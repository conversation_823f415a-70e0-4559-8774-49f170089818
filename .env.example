# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=water_tracker
DB_USERNAME=water_user
DB_PASSWORD=your_db_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DATABASE=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRATION=86400

# 文件上传配置
UPLOAD_PATH=/data/uploads/
MAX_FILE_SIZE=10MB

# 短信服务配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
SMS_SIGN_NAME=嘉依打卡
SMS_TEMPLATE_CODE=SMS_123456789

# 应用配置
APP_ENV=production
APP_DEBUG=false

# 监控配置
ENABLE_METRICS=true
ENABLE_HEALTH_CHECK=true
