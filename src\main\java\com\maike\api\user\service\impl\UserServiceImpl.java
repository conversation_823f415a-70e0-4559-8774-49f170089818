package com.maike.api.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maike.api.common.exception.BusinessException;
import com.maike.api.common.result.ResultCode;
import com.maike.api.common.utils.JwtUtils;
import com.maike.api.common.utils.PasswordUtils;
import com.maike.api.common.utils.ValidationUtils;
import com.maike.api.user.mapper.UserMapper;
import com.maike.api.user.pojo.dto.*;
import com.maike.api.user.pojo.entity.User;
import com.maike.api.user.pojo.vo.LoginVO;
import com.maike.api.user.pojo.vo.RegisterVO;
import com.maike.api.user.pojo.vo.UserInfoVO;
import com.maike.api.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final UserMapper userMapper;
    private final JwtUtils jwtUtils;
    private final StringRedisTemplate redisTemplate;

    /**
     * 验证码Redis键前缀
     */
    private static final String VERIFICATION_CODE_PREFIX = "verification_code:";

    /**
     * 验证码过期时间（分钟）
     */
    private static final int VERIFICATION_CODE_EXPIRE_MINUTES = 5;

    /**
     * 验证码长度
     */
    private static final int VERIFICATION_CODE_LENGTH = 6;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RegisterVO register(UserRegisterDTO registerDTO, String clientIp) {
        log.info("用户注册，手机号：{}", registerDTO.getPhoneNumber());

        // 1. 验证验证码
        if (!verifyCode(registerDTO.getPhoneNumber(), registerDTO.getVerificationCode(), "register")) {
            throw new BusinessException(ResultCode.VERIFICATION_CODE_ERROR);
        }

        // 2. 检查手机号是否已注册
        User existUser = getUserByPhoneNumber(registerDTO.getPhoneNumber());
        if (existUser != null) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
        }

        // 3. 检查昵称是否可用
        if (!isNicknameAvailable(registerDTO.getNickname(), null)) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS.getCode(), "昵称已被使用");
        }

        // 4. 创建用户
        User user = new User();
        user.setPhoneNumber(registerDTO.getPhoneNumber());
        user.setNickname(registerDTO.getNickname());
        user.setGender(StrUtil.isNotBlank(registerDTO.getGender()) ? registerDTO.getGender() : "unknown");
        user.setBirthday(registerDTO.getBirthday());
        
        // 5. 加密密码
        String salt = PasswordUtils.generateSalt();
        String encryptedPassword = PasswordUtils.encryptPasswordWithSalt(registerDTO.getPassword(), salt);
        user.setPassword(encryptedPassword);
        user.setSalt(salt);
        
        // 6. 设置其他信息
        user.setStatus("0");
        user.setLoginIp(clientIp);
        user.setLoginDate(LocalDateTime.now());
        user.setCreateBy("system");
        user.setUpdateBy("system");

        // 7. 保存用户
        if (!save(user)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "用户注册失败");
        }

        // 8. 删除验证码
        deleteVerificationCode(registerDTO.getPhoneNumber(), "register");

        // 9. 生成JWT令牌
        String token = jwtUtils.generateToken(user.getId(), user.getPhoneNumber());

        // 10. 构建返回结果
        RegisterVO registerVO = new RegisterVO();
        registerVO.setUserId(user.getId());
        registerVO.setToken(token);
        registerVO.setExpiresIn((Long) jwtUtils.getJwtConfig().get("expiration"));

        log.info("用户注册成功，用户ID：{}", user.getId());
        return registerVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginVO login(UserLoginDTO loginDTO, String clientIp) {
        log.info("用户登录，手机号：{}", loginDTO.getPhoneNumber());

        // 1. 查询用户
        User user = getUserByPhoneNumber(loginDTO.getPhoneNumber());
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 检查用户状态
        if (!user.isNormal()) {
            if (user.isDisabled()) {
                throw new BusinessException(ResultCode.USER_DISABLED);
            } else {
                throw new BusinessException(ResultCode.USER_STATUS_ERROR);
            }
        }

        // 3. 验证密码
        if (!PasswordUtils.verifyPasswordWithSalt(loginDTO.getPassword(), user.getSalt(), user.getPassword())) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }

        // 4. 更新登录信息
        updateLoginInfo(user.getId(), clientIp);

        // 5. 生成JWT令牌
        String token = jwtUtils.generateToken(user.getId(), user.getPhoneNumber());

        // 6. 构建返回结果
        LoginVO loginVO = new LoginVO();
        loginVO.setUserId(user.getId());
        loginVO.setToken(token);
        loginVO.setExpiresIn((Long) jwtUtils.getJwtConfig().get("expiration"));

        // 7. 设置用户基本信息
        LoginVO.UserBasicInfo userInfo = new LoginVO.UserBasicInfo();
        userInfo.setNickname(user.getNickname());
        userInfo.setAvatarUrl(user.getAvatarUrl());
        userInfo.setGender(user.getGender());
        userInfo.setCoupleId(user.getCoupleId());
        loginVO.setUserInfo(userInfo);

        log.info("用户登录成功，用户ID：{}", user.getId());
        return loginVO;
    }

    @Override
    public UserInfoVO getUserInfo(String userId) {
        log.info("获取用户信息，用户ID：{}", userId);

        User user = getById(userId);
        if (user == null || !user.isNormal()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtil.copyProperties(user, userInfoVO);
        
        // 脱敏手机号
        userInfoVO.setPhoneNumber(user.getMaskedPhoneNumber());
        
        // 设置性别显示文本
        userInfoVO.setGenderText(user.getGenderText());
        
        // 设置是否有情侣关系
        userInfoVO.setHasCouple(user.hasCouple());

        return userInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateUserInfo(String userId, UserUpdateDTO updateDTO) {
        log.info("更新用户信息，用户ID：{}", userId);

        User user = getById(userId);
        if (user == null || !user.isNormal()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        List<String> updatedFields = new ArrayList<>();

        // 更新昵称
        if (StrUtil.isNotBlank(updateDTO.getNickname()) && !updateDTO.getNickname().equals(user.getNickname())) {
            if (!isNicknameAvailable(updateDTO.getNickname(), userId)) {
                throw new BusinessException(ResultCode.USER_ALREADY_EXISTS.getCode(), "昵称已被使用");
            }
            user.setNickname(updateDTO.getNickname());
            updatedFields.add("nickname");
        }

        // 更新头像
        if (StrUtil.isNotBlank(updateDTO.getAvatarUrl()) && !updateDTO.getAvatarUrl().equals(user.getAvatarUrl())) {
            user.setAvatarUrl(updateDTO.getAvatarUrl());
            updatedFields.add("avatarUrl");
        }

        // 更新性别
        if (StrUtil.isNotBlank(updateDTO.getGender()) && !updateDTO.getGender().equals(user.getGender())) {
            user.setGender(updateDTO.getGender());
            updatedFields.add("gender");
        }

        // 更新生日
        if (updateDTO.getBirthday() != null && !updateDTO.getBirthday().equals(user.getBirthday())) {
            user.setBirthday(updateDTO.getBirthday());
            updatedFields.add("birthday");
        }

        // 更新个性签名
        if (updateDTO.getBio() != null && !updateDTO.getBio().equals(user.getBio())) {
            user.setBio(updateDTO.getBio());
            updatedFields.add("bio");
        }

        // 如果有更新，保存到数据库
        if (!updatedFields.isEmpty()) {
            user.setUpdateBy(userId);
            user.setUpdateTime(LocalDateTime.now());
            if (!updateById(user)) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "更新用户信息失败");
            }
        }

        log.info("用户信息更新成功，用户ID：{}，更新字段：{}", userId, updatedFields);
        return updatedFields;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(String userId, PasswordChangeDTO passwordChangeDTO) {
        log.info("修改密码，用户ID：{}", userId);

        User user = getById(userId);
        if (user == null || !user.isNormal()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 1. 验证验证码
        if (!verifyCode(user.getPhoneNumber(), passwordChangeDTO.getVerificationCode(), "change")) {
            throw new BusinessException(ResultCode.VERIFICATION_CODE_ERROR);
        }

        // 2. 验证原密码
        if (!PasswordUtils.verifyPasswordWithSalt(passwordChangeDTO.getOldPassword(), user.getSalt(), user.getPassword())) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR.getCode(), "原密码错误");
        }

        // 3. 检查新密码格式
        if (!PasswordUtils.isValidPassword(passwordChangeDTO.getNewPassword())) {
            throw new BusinessException(ResultCode.PASSWORD_FORMAT_ERROR);
        }

        // 4. 生成新的盐值和加密密码
        String newSalt = PasswordUtils.generateSalt();
        String newEncryptedPassword = PasswordUtils.encryptPasswordWithSalt(passwordChangeDTO.getNewPassword(), newSalt);

        // 5. 更新密码
        user.setPassword(newEncryptedPassword);
        user.setSalt(newSalt);
        user.setUpdateBy(userId);
        user.setUpdateTime(LocalDateTime.now());

        if (!updateById(user)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "密码修改失败");
        }

        // 6. 删除验证码
        deleteVerificationCode(user.getPhoneNumber(), "change");

        log.info("密码修改成功，用户ID：{}", userId);
    }

    @Override
    public Integer sendVerificationCode(SendCodeDTO sendCodeDTO) {
        log.info("发送验证码，手机号：{}，类型：{}", sendCodeDTO.getPhoneNumber(), sendCodeDTO.getType());

        // 1. 验证手机号格式
        if (!ValidationUtils.isValidPhone(sendCodeDTO.getPhoneNumber())) {
            throw new BusinessException(ResultCode.PHONE_FORMAT_ERROR);
        }

        // 2. 根据类型进行不同的业务验证
        switch (sendCodeDTO.getType()) {
            case "register":
                // 注册时检查手机号是否已存在
                if (getUserByPhoneNumber(sendCodeDTO.getPhoneNumber()) != null) {
                    throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
                }
                break;
            case "login":
            case "change":
            case "reset":
                // 登录、修改密码、重置密码时检查用户是否存在
                User user = getUserByPhoneNumber(sendCodeDTO.getPhoneNumber());
                if (user == null) {
                    throw new BusinessException(ResultCode.USER_NOT_FOUND);
                }
                if (!user.isNormal()) {
                    throw new BusinessException(ResultCode.USER_STATUS_ERROR);
                }
                break;
            default:
                throw new BusinessException(ResultCode.PARAM_ERROR.getCode(), "验证码类型不正确");
        }

        // 3. 生成验证码
        String code = RandomUtil.randomNumbers(VERIFICATION_CODE_LENGTH);

        // 4. 存储到Redis
        String key = VERIFICATION_CODE_PREFIX + sendCodeDTO.getType() + ":" + sendCodeDTO.getPhoneNumber();
        redisTemplate.opsForValue().set(key, code, VERIFICATION_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 5. 发送短信（这里模拟发送，实际项目中需要接入短信服务商）
        log.info("发送验证码：{}，手机号：{}", code, sendCodeDTO.getPhoneNumber());

        return VERIFICATION_CODE_EXPIRE_MINUTES * 60; // 返回过期时间（秒）
    }

    @Override
    public void logout(String userId, String token) {
        log.info("用户退出登录，用户ID：{}", userId);

        // 将token加入黑名单（可选实现）
        // 这里可以将token存储到Redis黑名单中，在JWT验证时检查
        String blacklistKey = "jwt_blacklist:" + token;
        long remainingTime = jwtUtils.getTokenRemainingTime(token);
        if (remainingTime > 0) {
            redisTemplate.opsForValue().set(blacklistKey, "1", remainingTime, TimeUnit.SECONDS);
        }

        log.info("用户退出登录成功，用户ID：{}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAccount(String userId, UserDeleteDTO deleteDTO) {
        log.info("删除用户账户，用户ID：{}", userId);

        User user = getById(userId);
        if (user == null || !user.isNormal()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 1. 验证验证码
        if (!verifyCode(user.getPhoneNumber(), deleteDTO.getVerificationCode(), "reset")) {
            throw new BusinessException(ResultCode.VERIFICATION_CODE_ERROR);
        }

        // 2. 验证密码
        if (!PasswordUtils.verifyPasswordWithSalt(deleteDTO.getPassword(), user.getSalt(), user.getPassword())) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR.getCode(), "密码错误");
        }

        // 3. 软删除用户
        if (userMapper.softDeleteUser(userId) <= 0) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "账户删除失败");
        }

        // 4. 删除验证码
        deleteVerificationCode(user.getPhoneNumber(), "reset");

        log.info("用户账户删除成功，用户ID：{}", userId);
    }

    @Override
    public User getUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectByPhoneNumber(phoneNumber);
    }

    @Override
    public boolean verifyCode(String phoneNumber, String code, String type) {
        String key = VERIFICATION_CODE_PREFIX + type + ":" + phoneNumber;
        String storedCode = redisTemplate.opsForValue().get(key);
        return StrUtil.isNotBlank(storedCode) && storedCode.equals(code);
    }

    @Override
    public boolean isNicknameAvailable(String nickname, String excludeUserId) {
        int count = userMapper.countByNickname(nickname, excludeUserId);
        return count == 0;
    }

    @Override
    public void updateLoginInfo(String userId, String loginIp) {
        userMapper.updateLoginInfo(userId, loginIp);
    }

    @Override
    public List<User> getUsersByIds(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(User::getId, userIds)
               .eq(User::getStatus, "0");

        return list(wrapper);
    }

    @Override
    public boolean isUserExistAndNormal(String userId) {
        if (StrUtil.isBlank(userId)) {
            return false;
        }

        User user = getById(userId);
        return user != null && user.isNormal();
    }

    /**
     * 删除验证码
     *
     * @param phoneNumber 手机号
     * @param type        验证码类型
     */
    private void deleteVerificationCode(String phoneNumber, String type) {
        String key = VERIFICATION_CODE_PREFIX + type + ":" + phoneNumber;
        redisTemplate.delete(key);
    }
}
