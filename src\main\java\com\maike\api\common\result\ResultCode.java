package com.maike.api.common.result;

/**
 * 响应状态码枚举
 * 定义系统中所有可能的响应状态码和对应的消息
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public enum ResultCode {

    // ========== 通用状态码 ==========
    /**
     * 操作成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 系统内部错误
     */
    ERROR(500, "系统内部错误"),

    /**
     * 请求参数错误
     */
    PARAM_ERROR(400, "请求参数错误"),

    /**
     * 未授权或token过期
     */
    UNAUTHORIZED(401, "未授权或token过期"),

    /**
     * 权限不足
     */
    FORBIDDEN(403, "权限不足"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 数据冲突
     */
    CONFLICT(409, "数据冲突"),

    /**
     * 请求过于频繁
     */
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // ========== 用户相关状态码 (1000-1999) ==========
    /**
     * 用户不存在
     */
    USER_NOT_FOUND(1001, "用户不存在"),

    /**
     * 用户名或密码错误
     */
    USER_LOGIN_ERROR(1002, "用户名或密码错误"),

    /**
     * 用户已存在
     */
    USER_ALREADY_EXISTS(1003, "用户已存在"),

    /**
     * 用户状态异常
     */
    USER_STATUS_ERROR(1004, "用户状态异常"),

    /**
     * 密码格式错误
     */
    PASSWORD_FORMAT_ERROR(1005, "密码格式错误"),

    /**
     * 手机号格式错误
     */
    PHONE_FORMAT_ERROR(1006, "手机号格式错误"),

    /**
     * 验证码错误
     */
    VERIFICATION_CODE_ERROR(1007, "验证码错误"),

    /**
     * 验证码已过期
     */
    VERIFICATION_CODE_EXPIRED(1008, "验证码已过期"),

    /**
     * 用户已被禁用
     */
    USER_DISABLED(1009, "用户已被禁用"),

    // ========== 情侣相关状态码 (2000-2999) ==========
    /**
     * 情侣关系不存在
     */
    COUPLE_NOT_FOUND(2001, "情侣关系不存在"),

    /**
     * 邀请码无效
     */
    INVITE_CODE_INVALID(2002, "邀请码无效"),

    /**
     * 邀请码已过期
     */
    INVITE_CODE_EXPIRED(2003, "邀请码已过期"),

    /**
     * 已存在情侣关系
     */
    COUPLE_ALREADY_EXISTS(2004, "已存在情侣关系"),

    /**
     * 不能邀请自己
     */
    CANNOT_INVITE_SELF(2005, "不能邀请自己"),

    /**
     * 情侣关系已解绑
     */
    COUPLE_UNBOUND(2006, "情侣关系已解绑"),

    // ========== 喝水相关状态码 (3000-3999) ==========
    /**
     * 喝水记录不存在
     */
    WATER_RECORD_NOT_FOUND(3001, "喝水记录不存在"),

    /**
     * 喝水量参数错误
     */
    WATER_AMOUNT_ERROR(3002, "喝水量参数错误"),

    /**
     * 今日目标已设置
     */
    DAILY_GOAL_ALREADY_SET(3003, "今日目标已设置"),

    /**
     * 目标不存在
     */
    GOAL_NOT_FOUND(3004, "目标不存在"),

    // ========== 视频相关状态码 (4000-4999) ==========
    /**
     * 视频不存在
     */
    VIDEO_NOT_FOUND(4001, "视频不存在"),

    /**
     * 视频格式不支持
     */
    VIDEO_FORMAT_NOT_SUPPORTED(4002, "视频格式不支持"),

    /**
     * 视频大小超出限制
     */
    VIDEO_SIZE_EXCEEDED(4003, "视频大小超出限制"),

    /**
     * 视频上传失败
     */
    VIDEO_UPLOAD_FAILED(4004, "视频上传失败"),

    /**
     * 无权限操作该视频
     */
    VIDEO_PERMISSION_DENIED(4005, "无权限操作该视频"),

    // ========== 成就相关状态码 (5000-5999) ==========
    /**
     * 成就不存在
     */
    ACHIEVEMENT_NOT_FOUND(5001, "成就不存在"),

    /**
     * 成就已解锁
     */
    ACHIEVEMENT_ALREADY_UNLOCKED(5002, "成就已解锁"),

    // ========== 挑战相关状态码 (6000-6999) ==========
    /**
     * 挑战不存在
     */
    CHALLENGE_NOT_FOUND(6001, "挑战不存在"),

    /**
     * 挑战已结束
     */
    CHALLENGE_ENDED(6002, "挑战已结束"),

    /**
     * 挑战未开始
     */
    CHALLENGE_NOT_STARTED(6003, "挑战未开始"),

    // ========== 文件相关状态码 (7000-7999) ==========
    /**
     * 文件不存在
     */
    FILE_NOT_FOUND(7001, "文件不存在"),

    /**
     * 文件格式不支持
     */
    FILE_FORMAT_NOT_SUPPORTED(7002, "文件格式不支持"),

    /**
     * 文件大小超出限制
     */
    FILE_SIZE_EXCEEDED(7003, "文件大小超出限制"),

    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAILED(7004, "文件上传失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String msg;

    /**
     * 构造方法
     * 
     * @param code 状态码
     * @param msg  消息
     */
    ResultCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 获取状态码
     * 
     * @return 状态码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取消息
     * 
     * @return 消息
     */
    public String getMsg() {
        return msg;
    }
}
