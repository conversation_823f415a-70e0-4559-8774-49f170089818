package com.maike.api.common.utils;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

import java.util.regex.Pattern;

/**
 * 数据验证工具类
 * 提供各种数据格式验证功能
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public class ValidationUtils {

    /**
     * 手机号正则表达式
     */
    private static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";

    /**
     * 邮箱正则表达式
     */
    private static final String EMAIL_PATTERN = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

    /**
     * 身份证号正则表达式
     */
    private static final String ID_CARD_PATTERN = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

    /**
     * 用户名正则表达式（4-20位字母、数字、下划线）
     */
    private static final String USERNAME_PATTERN = "^[a-zA-Z0-9_]{4,20}$";

    /**
     * 昵称正则表达式（2-20位中文、字母、数字）
     */
    private static final String NICKNAME_PATTERN = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,20}$";

    /**
     * 验证码正则表达式（4-6位数字）
     */
    private static final String VERIFICATION_CODE_PATTERN = "^\\d{4,6}$";

    /**
     * 邀请码正则表达式（6-10位字母数字）
     */
    private static final String INVITE_CODE_PATTERN = "^[A-Z0-9]{6,10}$";

    /**
     * 私有构造方法，防止实例化
     */
    private ValidationUtils() {
    }

    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidPhone(String phone) {
        return StrUtil.isNotBlank(phone) && ReUtil.isMatch(PHONE_PATTERN, phone);
    }

    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidEmail(String email) {
        return StrUtil.isNotBlank(email) && ReUtil.isMatch(EMAIL_PATTERN, email);
    }

    /**
     * 验证身份证号格式
     * 
     * @param idCard 身份证号
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return false;
        }
        
        // 基本格式验证
        if (!ReUtil.isMatch(ID_CARD_PATTERN, idCard)) {
            return false;
        }
        
        // 校验位验证
        return validateIdCardChecksum(idCard);
    }

    /**
     * 验证用户名格式
     * 
     * @param username 用户名
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidUsername(String username) {
        return StrUtil.isNotBlank(username) && ReUtil.isMatch(USERNAME_PATTERN, username);
    }

    /**
     * 验证昵称格式
     * 
     * @param nickname 昵称
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidNickname(String nickname) {
        return StrUtil.isNotBlank(nickname) && ReUtil.isMatch(NICKNAME_PATTERN, nickname);
    }

    /**
     * 验证验证码格式
     * 
     * @param code 验证码
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidVerificationCode(String code) {
        return StrUtil.isNotBlank(code) && ReUtil.isMatch(VERIFICATION_CODE_PATTERN, code);
    }

    /**
     * 验证邀请码格式
     * 
     * @param inviteCode 邀请码
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidInviteCode(String inviteCode) {
        return StrUtil.isNotBlank(inviteCode) && ReUtil.isMatch(INVITE_CODE_PATTERN, inviteCode);
    }

    /**
     * 验证年龄范围
     * 
     * @param age 年龄
     * @return true-合法，false-不合法
     */
    public static boolean isValidAge(Integer age) {
        return age != null && age >= 1 && age <= 150;
    }

    /**
     * 验证喝水量范围
     * 
     * @param amount 喝水量（毫升）
     * @return true-合法，false-不合法
     */
    public static boolean isValidWaterAmount(Integer amount) {
        return amount != null && amount > 0 && amount <= 5000;
    }

    /**
     * 验证每日目标范围
     * 
     * @param goal 每日目标（毫升）
     * @return true-合法，false-不合法
     */
    public static boolean isValidDailyGoal(Integer goal) {
        return goal != null && goal >= 500 && goal <= 10000;
    }

    /**
     * 验证视频时长范围
     * 
     * @param duration 视频时长（秒）
     * @return true-合法，false-不合法
     */
    public static boolean isValidVideoDuration(Integer duration) {
        return duration != null && duration > 0 && duration <= 300; // 最长5分钟
    }

    /**
     * 验证文件大小范围
     * 
     * @param size 文件大小（字节）
     * @param maxSize 最大大小（字节）
     * @return true-合法，false-不合法
     */
    public static boolean isValidFileSize(Long size, Long maxSize) {
        return size != null && maxSize != null && size > 0 && size <= maxSize;
    }

    /**
     * 验证字符串长度范围
     * 
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return true-合法，false-不合法
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (StrUtil.isBlank(str)) {
            return minLength == 0;
        }
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证数值范围
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @return true-合法，false-不合法
     */
    public static boolean isValidRange(Number value, Number min, Number max) {
        if (value == null || min == null || max == null) {
            return false;
        }
        double val = value.doubleValue();
        double minVal = min.doubleValue();
        double maxVal = max.doubleValue();
        return val >= minVal && val <= maxVal;
    }

    /**
     * 验证身份证校验位
     * 
     * @param idCard 身份证号
     * @return true-校验通过，false-校验失败
     */
    private static boolean validateIdCardChecksum(String idCard) {
        if (idCard.length() != 18) {
            return false;
        }
        
        try {
            // 权重因子
            int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
            // 校验码对应值
            char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
            
            int sum = 0;
            for (int i = 0; i < 17; i++) {
                sum += Character.getNumericValue(idCard.charAt(i)) * weights[i];
            }
            
            int mod = sum % 11;
            char checkCode = checkCodes[mod];
            
            return checkCode == idCard.charAt(17);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 脱敏手机号
     * 
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (!isValidPhone(phone)) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 脱敏邮箱
     * 
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (!isValidEmail(email)) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex <= 1) {
            return email;
        }
        String prefix = email.substring(0, 1);
        String suffix = email.substring(atIndex);
        return prefix + "***" + suffix;
    }

    /**
     * 脱敏身份证号
     * 
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIdCard(String idCard) {
        if (StrUtil.isBlank(idCard) || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
}
