# 生产环境配置
server:
  port: 8888
  servlet:
    context-path: /api/v1
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:water_tracker}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 1000
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.maike.api.*.pojo.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: status
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    root: INFO
    com.maike.api: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate: WARN
    com.zaxxer.hikari: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/water-backend.log
    max-size: 100MB
    max-history: 30

# JWT配置
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: ${JWT_EXPIRATION:86400}

# 文件上传配置
file:
  upload:
    path: ${UPLOAD_PATH:/data/uploads/}
    max-size: ${MAX_FILE_SIZE:10MB}
    allowed-types: jpg,jpeg,png,gif,mp4,mov

# 短信配置
sms:
  provider: ${SMS_PROVIDER:aliyun}
  access-key: ${SMS_ACCESS_KEY:}
  secret-key: ${SMS_SECRET_KEY:}
  sign-name: ${SMS_SIGN_NAME:嘉依打卡}
  template-code: ${SMS_TEMPLATE_CODE:SMS_123456789}

# 应用配置
app:
  name: 嘉依打卡：元气水杯与Ta的约定
  version: 1.0.0
  description: 一款专为情侣设计的喝水打卡应用

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger配置
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
