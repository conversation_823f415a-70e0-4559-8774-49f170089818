package com.maike.api.user.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 删除用户账户请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class UserDeleteDTO {

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{4,6}$", message = "验证码格式不正确")
    private String verificationCode;
}
