package com.maike.api.water.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 喝水目标实体类
 * 对应数据库表：water_goals
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("water_goals")
public class WaterGoal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 目标ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 每日目标喝水量，单位毫升，默认2000ml
     */
    @TableField("daily_goal_ml")
    private Integer dailyGoalMl;

    /**
     * 目标日期，每天一条记录
     */
    @TableField("goal_date")
    private LocalDate goalDate;

    /**
     * 实际完成量，单位毫升，实时更新
     */
    @TableField("actual_amount_ml")
    private Integer actualAmountMl;

    /**
     * 是否达成目标：1-已达成，0-未达成
     */
    @TableField("is_achieved")
    private Boolean isAchieved;

    /**
     * 达成目标的时间
     */
    @TableField("achievement_time")
    private LocalDateTime achievementTime;

    /**
     * 完成率，百分比，如85.50表示85.5%
     */
    @TableField("completion_rate")
    private BigDecimal completionRate;

    /**
     * 记录状态：0-正常，1-已删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断记录是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断记录是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.status);
    }

    /**
     * 更新实际完成量和完成率
     * 
     * @param actualAmount 实际完成量
     */
    public void updateActualAmount(Integer actualAmount) {
        this.actualAmountMl = actualAmount;
        updateCompletionRate();
        checkAchievement();
    }

    /**
     * 更新完成率
     */
    public void updateCompletionRate() {
        if (dailyGoalMl != null && dailyGoalMl > 0 && actualAmountMl != null) {
            BigDecimal rate = BigDecimal.valueOf(actualAmountMl)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(dailyGoalMl), 2, RoundingMode.HALF_UP);
            this.completionRate = rate;
        } else {
            this.completionRate = BigDecimal.ZERO;
        }
    }

    /**
     * 检查是否达成目标
     */
    public void checkAchievement() {
        if (actualAmountMl != null && dailyGoalMl != null && actualAmountMl >= dailyGoalMl) {
            if (!Boolean.TRUE.equals(isAchieved)) {
                this.isAchieved = true;
                this.achievementTime = LocalDateTime.now();
            }
        } else {
            this.isAchieved = false;
            this.achievementTime = null;
        }
    }

    /**
     * 获取完成率百分比（整数）
     * 
     * @return 完成率百分比
     */
    public Integer getCompletionRatePercent() {
        if (completionRate == null) {
            return 0;
        }
        return completionRate.intValue();
    }

    /**
     * 获取剩余需要喝水量
     * 
     * @return 剩余需要喝水量（毫升）
     */
    public Integer getRemainingAmount() {
        if (dailyGoalMl == null || actualAmountMl == null) {
            return dailyGoalMl != null ? dailyGoalMl : 0;
        }
        
        int remaining = dailyGoalMl - actualAmountMl;
        return Math.max(remaining, 0);
    }

    /**
     * 判断是否超额完成
     * 
     * @return true-超额完成，false-未超额
     */
    public boolean isOverAchieved() {
        return actualAmountMl != null && dailyGoalMl != null && actualAmountMl > dailyGoalMl;
    }
}
