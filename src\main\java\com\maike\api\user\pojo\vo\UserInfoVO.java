package com.maike.api.user.pojo.vo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class UserInfoVO {

    /**
     * 用户ID
     */
    private String id;

    /**
     * 手机号码（脱敏）
     */
    private String phoneNumber;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 性别
     */
    private String gender;

    /**
     * 性别显示文本
     */
    private String genderText;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 个性签名
     */
    private String bio;

    /**
     * 情侣关系ID
     */
    private String coupleId;

    /**
     * 是否有情侣关系
     */
    private Boolean hasCouple;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
