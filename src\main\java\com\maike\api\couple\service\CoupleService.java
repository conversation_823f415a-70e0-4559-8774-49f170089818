package com.maike.api.couple.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.couple.pojo.dto.CoupleAcceptDTO;
import com.maike.api.couple.pojo.dto.CoupleCreateDTO;
import com.maike.api.couple.pojo.dto.CoupleUpdateDTO;
import com.maike.api.couple.pojo.entity.Couple;
import com.maike.api.couple.pojo.vo.CoupleCreateVO;
import com.maike.api.couple.pojo.vo.CoupleInfoVO;

import java.util.List;

/**
 * 情侣关系服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface CoupleService extends IService<Couple> {

    /**
     * 创建情侣邀请
     * 
     * @param userId    用户ID
     * @param createDTO 创建信息
     * @return 创建结果
     */
    CoupleCreateVO createInvite(String userId, CoupleCreateDTO createDTO);

    /**
     * 接受情侣邀请
     * 
     * @param userId    用户ID
     * @param acceptDTO 接受邀请信息
     */
    void acceptInvite(String userId, CoupleAcceptDTO acceptDTO);

    /**
     * 获取情侣信息
     * 
     * @param userId 用户ID
     * @return 情侣信息
     */
    CoupleInfoVO getCoupleInfo(String userId);

    /**
     * 更新情侣信息
     * 
     * @param userId    用户ID
     * @param updateDTO 更新信息
     * @return 更新的字段列表
     */
    List<String> updateCoupleInfo(String userId, CoupleUpdateDTO updateDTO);

    /**
     * 解除情侣关系
     * 
     * @param userId 用户ID
     */
    void unbindCouple(String userId);

    /**
     * 根据邀请码查询情侣关系
     * 
     * @param inviteCode 邀请码
     * @return 情侣关系
     */
    Couple getCoupleByInviteCode(String inviteCode);

    /**
     * 根据用户ID查询情侣关系
     * 
     * @param userId 用户ID
     * @return 情侣关系
     */
    Couple getCoupleByUserId(String userId);

    /**
     * 生成唯一邀请码
     * 
     * @return 邀请码
     */
    String generateInviteCode();

    /**
     * 检查用户是否已有情侣关系
     * 
     * @param userId 用户ID
     * @return true-已有，false-没有
     */
    boolean hasCouple(String userId);

    /**
     * 更新情侣关系总天数
     * 
     * @param coupleId 情侣关系ID
     */
    void updateTotalDays(String coupleId);
}
