package com.maike.api.notification.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.notification.pojo.dto.ReminderCreateDTO;
import com.maike.api.water.pojo.entity.Reminder;
import com.maike.api.water.pojo.vo.ReminderVO;

import java.util.List;

/**
 * 提醒服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ReminderService extends IService<Reminder> {

    /**
     * 创建提醒
     * 
     * @param userId    用户ID
     * @param createDTO 创建信息
     * @return 提醒信息
     */
    ReminderVO createReminder(String userId, ReminderCreateDTO createDTO);

    /**
     * 获取用户提醒列表
     * 
     * @param userId 用户ID
     * @return 提醒列表
     */
    List<ReminderVO> getUserReminders(String userId);

    /**
     * 更新提醒
     * 
     * @param userId     用户ID
     * @param reminderId 提醒ID
     * @param updateDTO  更新信息
     * @return 更新的字段列表
     */
    List<String> updateReminder(String userId, String reminderId, ReminderCreateDTO updateDTO);

    /**
     * 删除提醒
     * 
     * @param userId     用户ID
     * @param reminderId 提醒ID
     */
    void deleteReminder(String userId, String reminderId);

    /**
     * 启用/禁用提醒
     * 
     * @param userId     用户ID
     * @param reminderId 提醒ID
     * @param isActive   是否启用
     */
    void toggleReminder(String userId, String reminderId, Boolean isActive);

    /**
     * 获取当前时间需要触发的提醒
     * 
     * @return 需要触发的提醒列表
     */
    List<Reminder> getTriggeredReminders();

    /**
     * 触发提醒
     * 
     * @param reminderId 提醒ID
     */
    void triggerReminder(String reminderId);

    /**
     * 延迟提醒
     * 
     * @param userId     用户ID
     * @param reminderId 提醒ID
     */
    void snoozeReminder(String userId, String reminderId);
}
