erDiagram
    USERS {
        string id PK
        string phone_number
        string nickname
        string avatar_url
        string gender
        date birthday
        string bio
        string couple_id FK
        datetime created_at
        datetime updated_at
    }
    
    COUPLES {
        string id PK
        string invite_code
        string user1_id FK
        string user2_id FK
        datetime bound_at
        boolean is_active
        datetime created_at
    }
    
    WATER_RECORDS {
        string id PK
        string user_id FK
        integer amount_ml
        datetime recorded_at
        string cup_type
        datetime created_at
    }
    
    WATER_GOALS {
        string id PK
        string user_id FK
        integer daily_goal_ml
        date goal_date
        boolean is_achieved
        datetime created_at
    }
    
    VIDEOS {
        string id PK
        string user_id FK
        string video_url
        string thumbnail_url
        string description
        string visibility
        integer duration_seconds
        datetime created_at
    }
    
    VIDEO_INTERACTIONS {
        string id PK
        string video_id FK
        string user_id FK
        string type
        string comment_text
        datetime created_at
    }
    
    ACHIEVEMENTS {
        string id PK
        string name
        string description
        string icon_url
        string type
        json requirements
        datetime created_at
    }
    
    USER_ACHIEVEMENTS {
        string id PK
        string user_id FK
        string achievement_id FK
        datetime unlocked_at
    }
    
    REMINDERS {
        string id PK
        string user_id FK
        time reminder_time
        string message
        boolean is_active
        string sound_type
        datetime created_at
    }
    
    CHALLENGES {
        string id PK
        string couple_id FK
        string name
        string description
        integer duration_days
        json requirements
        datetime start_date
        datetime end_date
        string status
        datetime created_at
    }
    
    NOTIFICATIONS {
        string id PK
        string user_id FK
        string type
        string title
        string message
        json data
        boolean is_read
        datetime created_at
    }

    USERS ||--o| COUPLES : belongs_to
    USERS ||--o{ WATER_RECORDS : has_many
    USERS ||--o{ WATER_GOALS : has_many
    USERS ||--o{ VIDEOS : has_many
    USERS ||--o{ VIDEO_INTERACTIONS : has_many
    USERS ||--o{ USER_ACHIEVEMENTS : has_many
    USERS ||--o{ REMINDERS : has_many
    USERS ||--o{ NOTIFICATIONS : has_many
    COUPLES ||--o{ CHALLENGES : has_many
    VIDEOS ||--o{ VIDEO_INTERACTIONS : has_many
    ACHIEVEMENTS ||--o{ USER_ACHIEVEMENTS : has_many