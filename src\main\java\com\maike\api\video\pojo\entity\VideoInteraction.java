package com.maike.api.video.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频互动实体类
 * 对应数据库表：video_interactions
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("video_interactions")
public class VideoInteraction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 互动ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 视频ID，关联videos表
     */
    @TableField("video_id")
    private String videoId;

    /**
     * 用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 互动类型：like-点赞，comment-评论，share-分享，report-举报
     */
    @TableField("type")
    private String type;

    /**
     * 评论内容，当type为comment时使用
     */
    @TableField("comment_text")
    private String commentText;

    /**
     * 表情类型，如heart、smile、love等
     */
    @TableField("emoji_type")
    private String emojiType;

    /**
     * 回复的评论ID，用于评论回复功能
     */
    @TableField("reply_to_id")
    private String replyToId;

    /**
     * 是否已删除：1-已删除，0-正常
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 操作IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 设备信息
     */
    @TableField("device_info")
    private String deviceInfo;

    /**
     * 记录状态：0-正常，1-已删除，2-违规
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断记录是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status) && !Boolean.TRUE.equals(this.isDeleted);
    }

    /**
     * 判断记录是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.status) || Boolean.TRUE.equals(this.isDeleted);
    }

    /**
     * 判断记录是否违规
     * 
     * @return true-违规，false-正常
     */
    public boolean isViolated() {
        return "2".equals(this.status);
    }

    /**
     * 判断是否为点赞类型
     * 
     * @return true-点赞，false-非点赞
     */
    public boolean isLike() {
        return "like".equals(this.type);
    }

    /**
     * 判断是否为评论类型
     * 
     * @return true-评论，false-非评论
     */
    public boolean isComment() {
        return "comment".equals(this.type);
    }

    /**
     * 判断是否为分享类型
     * 
     * @return true-分享，false-非分享
     */
    public boolean isShare() {
        return "share".equals(this.type);
    }

    /**
     * 判断是否为举报类型
     * 
     * @return true-举报，false-非举报
     */
    public boolean isReport() {
        return "report".equals(this.type);
    }

    /**
     * 判断是否为回复评论
     * 
     * @return true-回复评论，false-非回复评论
     */
    public boolean isReply() {
        return isComment() && this.replyToId != null && !this.replyToId.trim().isEmpty();
    }

    /**
     * 获取互动类型显示文本
     * 
     * @return 互动类型显示文本
     */
    public String getTypeText() {
        if (type == null) {
            return "未知";
        }
        
        switch (type) {
            case "like":
                return "点赞";
            case "comment":
                return "评论";
            case "share":
                return "分享";
            case "report":
                return "举报";
            default:
                return "未知";
        }
    }

    /**
     * 获取表情显示文本
     * 
     * @return 表情显示文本
     */
    public String getEmojiText() {
        if (emojiType == null) {
            return "❤️";
        }
        
        switch (emojiType) {
            case "heart":
                return "❤️";
            case "smile":
                return "😊";
            case "love":
                return "😍";
            case "laugh":
                return "😂";
            case "wow":
                return "😮";
            case "sad":
                return "😢";
            case "angry":
                return "😠";
            default:
                return "❤️";
        }
    }

    /**
     * 获取状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusText() {
        if (Boolean.TRUE.equals(isDeleted) || "1".equals(status)) {
            return "已删除";
        }
        
        if ("2".equals(status)) {
            return "违规";
        }
        
        return "正常";
    }
}
