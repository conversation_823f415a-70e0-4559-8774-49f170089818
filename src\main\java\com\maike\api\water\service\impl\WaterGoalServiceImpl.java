package com.maike.api.water.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maike.api.common.exception.BusinessException;
import com.maike.api.common.result.ResultCode;
import com.maike.api.user.service.UserService;
import com.maike.api.water.mapper.WaterGoalMapper;
import com.maike.api.water.pojo.dto.WaterGoalCreateDTO;
import com.maike.api.water.pojo.entity.WaterGoal;
import com.maike.api.water.pojo.vo.WaterGoalVO;
import com.maike.api.water.service.WaterGoalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 喝水目标服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WaterGoalServiceImpl extends ServiceImpl<WaterGoalMapper, WaterGoal> implements WaterGoalService {

    private final WaterGoalMapper waterGoalMapper;
    private final UserService userService;

    /**
     * 默认每日目标喝水量（毫升）
     */
    private static final int DEFAULT_DAILY_GOAL = 2000;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WaterGoalVO setDailyGoal(String userId, WaterGoalCreateDTO createDTO) {
        log.info("设置每日喝水目标，用户ID：{}", userId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 设置默认日期
        LocalDate goalDate = createDTO.getGoalDate();
        if (goalDate == null) {
            goalDate = LocalDate.now();
        }

        // 3. 查询是否已存在目标
        WaterGoal existingGoal = waterGoalMapper.selectByUserIdAndDate(userId, goalDate);
        
        WaterGoal goal;
        if (existingGoal != null) {
            // 更新现有目标
            existingGoal.setDailyGoalMl(createDTO.getDailyGoalMl());
            existingGoal.updateCompletionRate();
            existingGoal.checkAchievement();
            existingGoal.setUpdateBy(userId);
            
            if (!updateById(existingGoal)) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "更新目标失败");
            }
            goal = existingGoal;
        } else {
            // 创建新目标
            goal = new WaterGoal();
            goal.setUserId(userId);
            goal.setDailyGoalMl(createDTO.getDailyGoalMl());
            goal.setGoalDate(goalDate);
            goal.setActualAmountMl(0);
            goal.setIsAchieved(false);
            goal.setCompletionRate(BigDecimal.ZERO);
            goal.setStatus("0");
            goal.setCreateBy(userId);
            goal.setUpdateBy(userId);
            
            if (!save(goal)) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "创建目标失败");
            }
        }

        // 4. 转换为VO
        WaterGoalVO goalVO = new WaterGoalVO();
        BeanUtil.copyProperties(goal, goalVO);
        goalVO.setGoalId(goal.getId());
        goalVO.setCompletionRatePercent(goal.getCompletionRatePercent());
        goalVO.setRemainingAmount(goal.getRemainingAmount());
        goalVO.setIsOverAchieved(goal.isOverAchieved());

        log.info("每日喝水目标设置成功，目标ID：{}", goal.getId());
        return goalVO;
    }

    @Override
    public WaterGoalVO getUserGoal(String userId, LocalDate goalDate) {
        log.info("获取用户指定日期的目标，用户ID：{}，日期：{}", userId, goalDate);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 设置默认日期
        if (goalDate == null) {
            goalDate = LocalDate.now();
        }

        // 3. 查询目标
        WaterGoal goal = waterGoalMapper.selectByUserIdAndDate(userId, goalDate);
        if (goal == null) {
            // 如果不存在，创建默认目标
            goal = createDefaultGoal(userId, goalDate);
        }

        // 4. 转换为VO
        WaterGoalVO goalVO = new WaterGoalVO();
        BeanUtil.copyProperties(goal, goalVO);
        goalVO.setGoalId(goal.getId());
        goalVO.setCompletionRatePercent(goal.getCompletionRatePercent());
        goalVO.setRemainingAmount(goal.getRemainingAmount());
        goalVO.setIsOverAchieved(goal.isOverAchieved());

        return goalVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActualAmount(String userId, LocalDate goalDate, Integer actualAmount) {
        log.info("更新目标的实际完成量，用户ID：{}，日期：{}，实际完成量：{}", userId, goalDate, actualAmount);

        // 1. 获取或创建目标
        WaterGoal goal = getOrCreateUserGoal(userId, goalDate);

        // 2. 更新实际完成量
        if (waterGoalMapper.updateActualAmount(goal.getId(), actualAmount) <= 0) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "更新实际完成量失败");
        }
    }

    @Override
    public WaterGoal getOrCreateUserGoal(String userId, LocalDate goalDate) {
        WaterGoal goal = waterGoalMapper.selectByUserIdAndDate(userId, goalDate);
        if (goal == null) {
            goal = createDefaultGoal(userId, goalDate);
        }
        return goal;
    }

    @Override
    public List<WaterGoalVO> getUserGoalsByDateRange(String userId, LocalDate startDate, LocalDate endDate) {
        log.info("查询用户指定日期范围内的目标，用户ID：{}，开始日期：{}，结束日期：{}", userId, startDate, endDate);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询目标
        List<WaterGoal> goals = waterGoalMapper.selectUserGoalsByDateRange(userId, startDate, endDate);

        // 3. 转换为VO
        return goals.stream().map(goal -> {
            WaterGoalVO goalVO = new WaterGoalVO();
            BeanUtil.copyProperties(goal, goalVO);
            goalVO.setGoalId(goal.getId());
            goalVO.setCompletionRatePercent(goal.getCompletionRatePercent());
            goalVO.setRemainingAmount(goal.getRemainingAmount());
            goalVO.setIsOverAchieved(goal.isOverAchieved());
            return goalVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<WaterGoalVO> getUserRecentGoals(String userId, Integer limit) {
        log.info("查询用户最近的目标，用户ID：{}，限制数量：{}", userId, limit);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 设置默认值
        if (limit == null || limit < 1) {
            limit = 7;
        }

        // 3. 查询目标
        List<WaterGoal> goals = waterGoalMapper.selectUserRecentGoals(userId, limit);

        // 4. 转换为VO
        return goals.stream().map(goal -> {
            WaterGoalVO goalVO = new WaterGoalVO();
            BeanUtil.copyProperties(goal, goalVO);
            goalVO.setGoalId(goal.getId());
            goalVO.setCompletionRatePercent(goal.getCompletionRatePercent());
            goalVO.setRemainingAmount(goal.getRemainingAmount());
            goalVO.setIsOverAchieved(goal.isOverAchieved());
            return goalVO;
        }).collect(Collectors.toList());
    }

    @Override
    public GoalAchievementStatVO getUserAchievementStats(String userId, LocalDate startDate, LocalDate endDate) {
        log.info("获取用户目标达成统计，用户ID：{}，开始日期：{}，结束日期：{}", userId, startDate, endDate);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询统计数据
        WaterGoalMapper.GoalAchievementStat stat = waterGoalMapper.selectUserAchievementStats(userId, startDate, endDate);

        // 3. 转换为VO
        GoalAchievementStatVO statVO = new GoalAchievementStatVO();
        if (stat != null) {
            statVO.setTotalGoals(stat.getTotalGoals());
            statVO.setAchievedGoals(stat.getAchievedGoals());
            statVO.setAchievementRate(stat.getAchievementRate());
        } else {
            statVO.setTotalGoals(0);
            statVO.setAchievedGoals(0);
            statVO.setAchievementRate(0.0);
        }

        return statVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndUpdateAchievement(String userId, LocalDate goalDate) {
        log.info("检查并更新目标达成状态，用户ID：{}，日期：{}", userId, goalDate);

        WaterGoal goal = waterGoalMapper.selectByUserIdAndDate(userId, goalDate);
        if (goal != null) {
            goal.checkAchievement();
            goal.updateCompletionRate();
            
            String achievementTime = goal.getAchievementTime() != null ? 
                goal.getAchievementTime().toString() : null;
            String completionRate = goal.getCompletionRate() != null ? 
                goal.getCompletionRate().toString() : "0.00";
                
            waterGoalMapper.updateAchievementStatus(
                goal.getId(), 
                goal.getIsAchieved(), 
                achievementTime, 
                completionRate
            );
        }
    }

    /**
     * 创建默认目标
     * 
     * @param userId   用户ID
     * @param goalDate 目标日期
     * @return 默认目标
     */
    private WaterGoal createDefaultGoal(String userId, LocalDate goalDate) {
        WaterGoal goal = new WaterGoal();
        goal.setUserId(userId);
        goal.setDailyGoalMl(DEFAULT_DAILY_GOAL);
        goal.setGoalDate(goalDate);
        goal.setActualAmountMl(0);
        goal.setIsAchieved(false);
        goal.setCompletionRate(BigDecimal.ZERO);
        goal.setStatus("0");
        goal.setCreateBy(userId);
        goal.setUpdateBy(userId);
        
        if (!save(goal)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "创建默认目标失败");
        }
        
        return goal;
    }
}
