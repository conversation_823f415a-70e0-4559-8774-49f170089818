<template>
  <view class="settings-page">
    <!-- 个人设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">个人设置</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item" @click="editProfile">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/user.png"></image>
            <text class="setting-text">个人资料</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ userInfo.nickname }}</text>
            <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
          </view>
        </view>
        
        <view class="setting-item" @click="changePassword">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/lock.png"></image>
            <text class="setting-text">修改密码</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
        
        <view class="setting-item" @click="setDailyGoal">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/target.png"></image>
            <text class="setting-text">每日目标</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ dailyGoal }}ml</text>
            <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 提醒设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">提醒设置</text>
        <view class="add-reminder" @click="addReminder">
          <image class="add-icon" src="/static/icons/plus.png"></image>
        </view>
      </view>
      
      <view class="settings-group">
        <view class="reminder-item" v-for="reminder in reminderList" :key="reminder.id">
          <view class="reminder-info">
            <text class="reminder-time">{{ reminder.reminder_time }}</text>
            <text class="reminder-message">{{ reminder.message }}</text>
            <text class="reminder-days">{{ formatRepeatDays(reminder.repeat_days) }}</text>
          </view>
          
          <view class="reminder-actions">
            <switch 
              :checked="reminder.is_active" 
              @change="toggleReminder(reminder)"
              color="#FF69B4"
            />
            <view class="edit-reminder" @click="editReminder(reminder)">
              <image class="edit-icon" src="/static/icons/edit.png"></image>
            </view>
          </view>
        </view>
        
        <view class="empty-reminders" v-if="reminderList.length === 0">
          <text>还没有设置提醒</text>
          <text class="add-first-reminder" @click="addReminder">添加第一个提醒</text>
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">应用设置</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/notification.png"></image>
            <text class="setting-text">推送通知</text>
          </view>
          <switch 
            :checked="appSettings.push_enabled" 
            @change="togglePushNotification"
            color="#FF69B4"
          />
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/sound.png"></image>
            <text class="setting-text">提醒音效</text>
          </view>
          <switch 
            :checked="appSettings.sound_enabled" 
            @change="toggleSound"
            color="#FF69B4"
          />
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/vibrate.png"></image>
            <text class="setting-text">震动提醒</text>
          </view>
          <switch 
            :checked="appSettings.vibrate_enabled" 
            @change="toggleVibrate"
            color="#FF69B4"
          />
        </view>
        
        <view class="setting-item" @click="selectTheme">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/theme.png"></image>
            <text class="setting-text">主题风格</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ getThemeName(appSettings.theme) }}</text>
            <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 隐私设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">隐私设置</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/privacy.png"></image>
            <text class="setting-text">数据统计可见</text>
          </view>
          <switch 
            :checked="privacySettings.stats_visible" 
            @change="toggleStatsVisible"
            color="#FF69B4"
          />
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/video-privacy.png"></image>
            <text class="setting-text">视频默认可见范围</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ getVisibilityName(privacySettings.default_video_visibility) }}</text>
            <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 其他设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">其他</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item" @click="clearCache">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/clean.png"></image>
            <text class="setting-text">清理缓存</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ cacheSize }}</text>
            <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
          </view>
        </view>
        
        <view class="setting-item" @click="checkUpdate">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/update.png"></image>
            <text class="setting-text">检查更新</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ appVersion }}</text>
            <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
          </view>
        </view>
        
        <view class="setting-item" @click="aboutApp">
          <view class="setting-left">
            <image class="setting-icon" src="/static/icons/info.png"></image>
            <text class="setting-text">关于应用</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
      </view>
    </view>

    <!-- 提醒设置弹窗 -->
    <uni-popup ref="reminderPopup" type="center">
      <view class="reminder-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEditingReminder ? '编辑提醒' : '添加提醒' }}</text>
        </view>
        
        <view class="popup-content">
          <view class="form-item">
            <text class="form-label">提醒时间</text>
            <picker mode="time" :value="currentReminder.reminder_time" @change="onTimeChange">
              <view class="time-picker">
                <text>{{ currentReminder.reminder_time || '选择时间' }}</text>
                <image class="picker-icon" src="/static/icons/clock.png"></image>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">提醒内容</text>
            <input 
              class="form-input" 
              v-model="currentReminder.message" 
              placeholder="输入提醒内容"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">重复日期</text>
            <view class="weekday-options">
              <view 
                class="weekday-option" 
                v-for="(day, index) in weekdays" 
                :key="index"
                :class="{ active: selectedDays.includes(index + 1) }"
                @click="toggleWeekday(index + 1)"
              >
                <text>{{ day }}</text>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">提醒音效</text>
            <view class="sound-options">
              <view 
                class="sound-option" 
                v-for="sound in soundTypes" 
                :key="sound.value"
                :class="{ active: currentReminder.sound_type === sound.value }"
                @click="currentReminder.sound_type = sound.value"
              >
                <text>{{ sound.label }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="popup-actions">
          <button class="cancel-btn" @click="closeReminderPopup">取消</button>
          <button class="save-btn" @click="saveReminder">保存</button>
          <button 
            class="delete-btn" 
            v-if="isEditingReminder" 
            @click="deleteReminder"
          >
            删除
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

// 响应式数据
const userInfo = ref({
  nickname: '小可爱'
})

const dailyGoal = ref(2000)
const reminderList = ref([])
const cacheSize = ref('12.5MB')
const appVersion = ref('1.0.0')

const appSettings = ref({
  push_enabled: true,
  sound_enabled: true,
  vibrate_enabled: true,
  theme: 'pink'
})

const privacySettings = ref({
  stats_visible: true,
  default_video_visibility: 'couple'
})

const currentReminder = ref({
  reminder_time: '',
  message: '该喝水啦！',
  sound_type: 'default',
  repeat_days: '1,2,3,4,5,6,7'
})

const isEditingReminder = ref(false)
const selectedDays = ref([1, 2, 3, 4, 5, 6, 7])

// 配置数据
const weekdays = ref(['一', '二', '三', '四', '五', '六', '日'])
const soundTypes = ref([
  { value: 'default', label: '默认' },
  { value: 'bubble', label: '泡泡音' },
  { value: 'sweet', label: '甜美音' },
  { value: 'gentle', label: '轻柔音' }
])

// 弹窗引用
const reminderPopup = ref()

// 页面加载时获取数据
onMounted(() => {
  loadUserInfo()
  loadSettings()
  loadReminders()
})

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await request.get('/user')
    userInfo.value = response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取设置
const loadSettings = async () => {
  try {
    // 这里可以从本地存储或服务器获取设置
    const settings = uni.getStorageSync('appSettings')
    if (settings) {
      appSettings.value = { ...appSettings.value, ...settings }
    }
  } catch (error) {
    console.error('获取设置失败:', error)
  }
}

// 获取提醒列表
const loadReminders = async () => {
  try {
    const response = await request.get('/reminder')
    reminderList.value = response.data.reminders
  } catch (error) {
    console.error('获取提醒列表失败:', error)
  }
}

// 页面跳转方法
const editProfile = () => {
  uni.navigateTo({ url: '/pages/profile/edit' })
}

const changePassword = () => {
  uni.navigateTo({ url: '/pages/auth/change-password' })
}

const setDailyGoal = () => {
  uni.showModal({
    title: '设置每日目标',
    editable: true,
    placeholderText: '请输入目标水量(ml)',
    success: async (res) => {
      if (res.confirm && res.content) {
        const goal = parseInt(res.content)
        if (goal > 0) {
          try {
            await request.post('/water/goal', {
              daily_goal_ml: goal,
              goal_date: new Date().toISOString().split('T')[0]
            })
            dailyGoal.value = goal
            uni.showToast({
              title: '目标设置成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('设置目标失败:', error)
          }
        }
      }
    }
  })
}

// 提醒相关方法
const addReminder = () => {
  isEditingReminder.value = false
  currentReminder.value = {
    reminder_time: '',
    message: '该喝水啦！',
    sound_type: 'default',
    repeat_days: '1,2,3,4,5,6,7'
  }
  selectedDays.value = [1, 2, 3, 4, 5, 6, 7]
  reminderPopup.value.open()
}

const editReminder = (reminder: any) => {
  isEditingReminder.value = true
  currentReminder.value = { ...reminder }
  selectedDays.value = reminder.repeat_days.split(',').map(Number)
  reminderPopup.value.open()
}

const closeReminderPopup = () => {
  reminderPopup.value.close()
}

const onTimeChange = (e: any) => {
  currentReminder.value.reminder_time = e.detail.value
}

const toggleWeekday = (day: number) => {
  const index = selectedDays.value.indexOf(day)
  if (index > -1) {
    selectedDays.value.splice(index, 1)
  } else {
    selectedDays.value.push(day)
  }
  selectedDays.value.sort()
}

const saveReminder = async () => {
  if (!currentReminder.value.reminder_time) {
    uni.showToast({
      title: '请选择提醒时间',
      icon: 'none'
    })
    return
  }
  
  try {
    currentReminder.value.repeat_days = selectedDays.value.join(',')
    
    if (isEditingReminder.value) {
      await request.put(`/reminder/${currentReminder.value.id}`, currentReminder.value)
    } else {
      await request.post('/reminder', currentReminder.value)
    }
    
    closeReminderPopup()
    loadReminders()
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('保存提醒失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  }
}

const deleteReminder = async () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个提醒吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await request.delete(`/reminder/${currentReminder.value.id}`)
          closeReminderPopup()
          loadReminders()
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('删除提醒失败:', error)
        }
      }
    }
  })
}

const toggleReminder = async (reminder: any) => {
  try {
    await request.put(`/reminder/${reminder.id}`, {
      is_active: !reminder.is_active
    })
    
    reminder.is_active = !reminder.is_active
  } catch (error) {
    console.error('切换提醒状态失败:', error)
  }
}

// 应用设置方法
const togglePushNotification = (e: any) => {
  appSettings.value.push_enabled = e.detail.value
  saveAppSettings()
}

const toggleSound = (e: any) => {
  appSettings.value.sound_enabled = e.detail.value
  saveAppSettings()
}

const toggleVibrate = (e: any) => {
  appSettings.value.vibrate_enabled = e.detail.value
  saveAppSettings()
}

const toggleStatsVisible = (e: any) => {
  privacySettings.value.stats_visible = e.detail.value
  savePrivacySettings()
}

const saveAppSettings = () => {
  uni.setStorageSync('appSettings', appSettings.value)
}

const savePrivacySettings = () => {
  uni.setStorageSync('privacySettings', privacySettings.value)
}

const selectTheme = () => {
  uni.showActionSheet({
    itemList: ['粉嫩可爱', '清新自然', '简约黑白'],
    success: (res) => {
      const themes = ['pink', 'green', 'dark']
      appSettings.value.theme = themes[res.tapIndex]
      saveAppSettings()
    }
  })
}

// 其他方法
const clearCache = () => {
  uni.showModal({
    title: '清理缓存',
    content: '确定要清理应用缓存吗？',
    success: (res) => {
      if (res.confirm) {
        // 清理缓存逻辑
        uni.showToast({
          title: '缓存清理完成',
          icon: 'success'
        })
        cacheSize.value = '0MB'
      }
    }
  })
}

const checkUpdate = () => {
  uni.showToast({
    title: '已是最新版本',
    icon: 'success'
  })
}

const aboutApp = () => {
  uni.navigateTo({ url: '/pages/about/about' })
}

// 工具方法
const formatRepeatDays = (repeatDays: string) => {
  const days = repeatDays.split(',').map(Number)
  if (days.length === 7) return '每天'
  if (days.length === 5 && !days.includes(6) && !days.includes(7)) return '工作日'
  if (days.length === 2 && days.includes(6) && days.includes(7)) return '周末'
  
  const dayNames = ['', '一', '二', '三', '四', '五', '六', '日']
  return '周' + days.map(d => dayNames[d]).join('、')
}

const getThemeName = (theme: string) => {
  const themeMap = {
    pink: '粉嫩可爱',
    green: '清新自然',
    dark: '简约黑白'
  }
  return themeMap[theme] || '粉嫩可爱'
}

const getVisibilityName = (visibility: string) => {
  const visibilityMap = {
    private: '仅自己',
    couple: '情侣可见',
    public: '公开'
  }
  return visibilityMap[visibility] || '情侣可见'
}
</script>

<style lang="scss" scoped>
.settings-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
  padding: 0 32rpx 32rpx;
}

/* 设置区域样式 */
.settings-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 0 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.add-reminder {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.3);
}

.add-icon {
  width: 24rpx;
  height: 24rpx;
}

.settings-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

/* 设置项样式 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #FFF0F5;
  transition: all 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background: #FFF0F5;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.setting-text {
  font-size: 28rpx;
  color: #333;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.setting-value {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 提醒项样式 */
.reminder-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #FFF0F5;
}

.reminder-item:last-child {
  border-bottom: none;
}

.reminder-info {
  flex: 1;
}

.reminder-time {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 8rpx;
}

.reminder-message {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.reminder-days {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.reminder-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.edit-reminder {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFF0F5;
  border-radius: 50%;
}

.edit-icon {
  width: 24rpx;
  height: 24rpx;
}

.empty-reminders {
  text-align: center;
  padding: 64rpx 32rpx;
}

.empty-reminders text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.add-first-reminder {
  color: #FF69B4 !important;
  font-weight: bold;
}

/* 提醒设置弹窗样式 */
.reminder-popup {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  width: 85vw;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.popup-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-content {
  margin-bottom: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid #FFE4E1;
}

.time-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #FFE4E1;
}

.picker-icon {
  width: 24rpx;
  height: 24rpx;
}

.weekday-options {
  display: flex;
  gap: 12rpx;
}

.weekday-option {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  background: #FFF0F5;
  border-radius: 12rpx;
  border: 2rpx solid #FFE4E1;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.weekday-option.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border-color: #FF69B4;
}

.sound-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.sound-option {
  text-align: center;
  padding: 16rpx;
  background: #FFF0F5;
  border-radius: 12rpx;
  border: 2rpx solid #FFE4E1;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.sound-option.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border-color: #FF69B4;
}

.popup-actions {
  display: flex;
  gap: 16rpx;
}

.cancel-btn, .save-btn, .delete-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.cancel-btn {
  background: #F5F5F5;
  color: #666;
}

.save-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

.delete-btn {
  background: linear-gradient(135deg, #FFA07A, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 160, 122, 0.3);
}

/* Switch组件样式调整 */
switch {
  transform: scale(0.8);
}
</style>
