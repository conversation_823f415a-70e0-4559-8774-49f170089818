package com.maike.api.water.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.water.pojo.dto.WaterRecordCreateDTO;
import com.maike.api.water.pojo.dto.WaterRecordUpdateDTO;
import com.maike.api.water.pojo.entity.WaterRecord;
import com.maike.api.water.pojo.vo.WaterRecordCreateVO;
import com.maike.api.water.pojo.vo.WaterRecordVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 喝水记录服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface WaterRecordService extends IService<WaterRecord> {

    /**
     * 添加喝水记录
     * 
     * @param userId    用户ID
     * @param createDTO 创建信息
     * @return 创建结果
     */
    WaterRecordCreateVO addWaterRecord(String userId, WaterRecordCreateDTO createDTO);

    /**
     * 分页查询用户喝水记录
     * 
     * @param userId 用户ID
     * @param date   查询日期（可选）
     * @param page   页码
     * @param size   每页数量
     * @return 分页结果
     */
    IPage<WaterRecordVO> getUserRecordsPage(String userId, LocalDate date, Integer page, Integer size);

    /**
     * 查询用户指定日期的喝水记录
     * 
     * @param userId 用户ID
     * @param date   查询日期
     * @return 喝水记录列表
     */
    List<WaterRecordVO> getUserRecordsByDate(String userId, LocalDate date);

    /**
     * 更新喝水记录
     * 
     * @param userId    用户ID
     * @param recordId  记录ID
     * @param updateDTO 更新信息
     * @return 更新的字段列表
     */
    List<String> updateWaterRecord(String userId, String recordId, WaterRecordUpdateDTO updateDTO);

    /**
     * 删除喝水记录
     * 
     * @param userId   用户ID
     * @param recordId 记录ID
     */
    void deleteWaterRecord(String userId, String recordId);

    /**
     * 计算用户指定日期的总喝水量
     * 
     * @param userId 用户ID
     * @param date   查询日期
     * @return 总喝水量（毫升）
     */
    Integer getUserDailyAmount(String userId, LocalDate date);

    /**
     * 查询用户最近的喝水记录
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 喝水记录列表
     */
    List<WaterRecordVO> getUserRecentRecords(String userId, Integer limit);

    /**
     * 获取用户喝水统计数据
     * 
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计数据
     */
    List<DailyWaterStatVO> getUserWaterStats(String userId, LocalDate startDate, LocalDate endDate);

    /**
     * 每日喝水统计VO
     */
    class DailyWaterStatVO {
        private LocalDate date;
        private Integer totalAmount;
        private Integer recordCount;

        // getters and setters
        public LocalDate getDate() { return date; }
        public void setDate(LocalDate date) { this.date = date; }
        public Integer getTotalAmount() { return totalAmount; }
        public void setTotalAmount(Integer totalAmount) { this.totalAmount = totalAmount; }
        public Integer getRecordCount() { return recordCount; }
        public void setRecordCount(Integer recordCount) { this.recordCount = recordCount; }
    }
}
