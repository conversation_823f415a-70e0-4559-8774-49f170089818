package com.maike.api.water.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.maike.api.water.pojo.entity.WaterGoal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 喝水目标Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface WaterGoalMapper extends BaseMapper<WaterGoal> {

    /**
     * 根据用户ID和日期查询目标
     * 
     * @param userId   用户ID
     * @param goalDate 目标日期
     * @return 喝水目标
     */
    WaterGoal selectByUserIdAndDate(@Param("userId") String userId, @Param("goalDate") LocalDate goalDate);

    /**
     * 查询用户指定日期范围内的目标
     * 
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 目标列表
     */
    List<WaterGoal> selectUserGoalsByDateRange(@Param("userId") String userId, 
                                               @Param("startDate") LocalDate startDate, 
                                               @Param("endDate") LocalDate endDate);

    /**
     * 更新目标的实际完成量
     * 
     * @param goalId       目标ID
     * @param actualAmount 实际完成量
     * @return 更新行数
     */
    int updateActualAmount(@Param("goalId") String goalId, @Param("actualAmount") Integer actualAmount);

    /**
     * 更新目标达成状态
     * 
     * @param goalId          目标ID
     * @param isAchieved      是否达成
     * @param achievementTime 达成时间
     * @param completionRate  完成率
     * @return 更新行数
     */
    int updateAchievementStatus(@Param("goalId") String goalId, 
                                @Param("isAchieved") Boolean isAchieved,
                                @Param("achievementTime") String achievementTime,
                                @Param("completionRate") String completionRate);

    /**
     * 查询用户最近的目标
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 目标列表
     */
    List<WaterGoal> selectUserRecentGoals(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 统计用户目标达成情况
     * 
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 达成统计
     */
    GoalAchievementStat selectUserAchievementStats(@Param("userId") String userId, 
                                                   @Param("startDate") LocalDate startDate, 
                                                   @Param("endDate") LocalDate endDate);

    /**
     * 目标达成统计内部类
     */
    class GoalAchievementStat {
        private Integer totalGoals;
        private Integer achievedGoals;
        private Double achievementRate;

        // getters and setters
        public Integer getTotalGoals() { return totalGoals; }
        public void setTotalGoals(Integer totalGoals) { this.totalGoals = totalGoals; }
        public Integer getAchievedGoals() { return achievedGoals; }
        public void setAchievedGoals(Integer achievedGoals) { this.achievedGoals = achievedGoals; }
        public Double getAchievementRate() { return achievementRate; }
        public void setAchievementRate(Double achievementRate) { this.achievementRate = achievementRate; }
    }
}
