package com.maike.api.user.pojo.vo;

import lombok.Data;

/**
 * 登录响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class LoginVO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * JWT令牌
     */
    private String token;

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户基本信息
     */
    private UserBasicInfo userInfo;

    /**
     * 用户基本信息内部类
     */
    @Data
    public static class UserBasicInfo {
        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 头像URL
         */
        private String avatarUrl;

        /**
         * 性别
         */
        private String gender;

        /**
         * 情侣关系ID
         */
        private String coupleId;
    }
}
