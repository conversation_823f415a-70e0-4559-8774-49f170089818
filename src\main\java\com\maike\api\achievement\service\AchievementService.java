package com.maike.api.achievement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.achievement.pojo.entity.Achievement;
import com.maike.api.achievement.pojo.vo.AchievementVO;
import com.maike.api.achievement.pojo.vo.UserAchievementStatsVO;

import java.util.List;

/**
 * 成就服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface AchievementService extends IService<Achievement> {

    /**
     * 获取成就列表
     * 
     * @param userId   用户ID
     * @param type     成就类型（可选）
     * @param category 成就分类（可选）
     * @return 成就列表
     */
    List<AchievementVO> getAchievementList(String userId, String type, String category);

    /**
     * 获取用户成就统计
     * 
     * @param userId 用户ID
     * @return 用户成就统计
     */
    UserAchievementStatsVO getUserAchievementStats(String userId);

    /**
     * 检查并解锁成就
     * 
     * @param userId 用户ID
     * @param event  触发事件
     * @param params 事件参数
     */
    void checkAndUnlockAchievements(String userId, String event, Object... params);

    /**
     * 手动授予成就
     * 
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @param reason        授予原因
     */
    void grantAchievement(String userId, String achievementId, String reason);

    /**
     * 获取用户未通知的成就
     * 
     * @param userId 用户ID
     * @return 未通知的成就列表
     */
    List<AchievementVO> getUnnotifiedAchievements(String userId);

    /**
     * 标记成就为已通知
     * 
     * @param userId        用户ID
     * @param achievementId 成就ID
     */
    void markAchievementAsNotified(String userId, String achievementId);

    /**
     * 获取成就详情
     * 
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @return 成就详情
     */
    AchievementVO getAchievementDetail(String userId, String achievementId);

    /**
     * 检查喝水相关成就
     * 
     * @param userId 用户ID
     */
    void checkWaterAchievements(String userId);

    /**
     * 检查视频相关成就
     * 
     * @param userId 用户ID
     */
    void checkVideoAchievements(String userId);

    /**
     * 检查社交相关成就
     * 
     * @param userId 用户ID
     */
    void checkSocialAchievements(String userId);

    /**
     * 检查情侣相关成就
     * 
     * @param userId 用户ID
     */
    void checkCoupleAchievements(String userId);
}
