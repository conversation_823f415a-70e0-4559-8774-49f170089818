package com.maike.api.water.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 喝水提醒实体类
 * 对应数据库表：reminders
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("reminders")
public class Reminder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提醒ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 提醒时间，如09:00、14:30
     */
    @TableField("reminder_time")
    private LocalTime reminderTime;

    /**
     * 提醒消息内容，支持个性化定制
     */
    @TableField("message")
    private String message;

    /**
     * 是否启用：1-启用，0-禁用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 提醒音效类型：default-默认，bubble-泡泡音，sweet-甜美音
     */
    @TableField("sound_type")
    private String soundType;

    /**
     * 重复日期，1-7表示周一到周日，逗号分隔
     */
    @TableField("repeat_days")
    private String repeatDays;

    /**
     * 延迟提醒分钟数，默认10分钟
     */
    @TableField("snooze_minutes")
    private Integer snoozeMinutes;

    /**
     * 提醒优先级：1-低，2-中，3-高
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 记录状态：0-正常，1-已删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断记录是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断记录是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.status);
    }

    /**
     * 获取重复日期列表
     * 
     * @return 重复日期列表
     */
    public List<Integer> getRepeatDaysList() {
        if (repeatDays == null || repeatDays.trim().isEmpty()) {
            return Arrays.asList(1, 2, 3, 4, 5, 6, 7); // 默认每天
        }
        
        return Arrays.stream(repeatDays.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 设置重复日期列表
     * 
     * @param daysList 重复日期列表
     */
    public void setRepeatDaysList(List<Integer> daysList) {
        if (daysList == null || daysList.isEmpty()) {
            this.repeatDays = "1,2,3,4,5,6,7";
        } else {
            this.repeatDays = daysList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
    }

    /**
     * 获取优先级显示文本
     * 
     * @return 优先级显示文本
     */
    public String getPriorityText() {
        if (priority == null) {
            return "中";
        }
        
        switch (priority) {
            case 1:
                return "低";
            case 2:
                return "中";
            case 3:
                return "高";
            default:
                return "中";
        }
    }

    /**
     * 获取音效类型显示文本
     * 
     * @return 音效类型显示文本
     */
    public String getSoundTypeText() {
        if (soundType == null) {
            return "默认";
        }
        
        switch (soundType) {
            case "default":
                return "默认";
            case "bubble":
                return "泡泡音";
            case "sweet":
                return "甜美音";
            default:
                return "默认";
        }
    }

    /**
     * 判断今天是否需要提醒
     * 
     * @param dayOfWeek 星期几（1-7，1表示周一）
     * @return true-需要提醒，false-不需要提醒
     */
    public boolean shouldRemindToday(int dayOfWeek) {
        if (!Boolean.TRUE.equals(isActive) || !isNormal()) {
            return false;
        }
        
        List<Integer> repeatDaysList = getRepeatDaysList();
        return repeatDaysList.contains(dayOfWeek);
    }
}
