package com.maike.api.video.pojo.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 视频上传请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class VideoUploadDTO {

    /**
     * 视频文件
     */
    private MultipartFile videoFile;

    /**
     * 缩略图文件（可选）
     */
    private MultipartFile thumbnailFile;

    /**
     * 视频描述
     */
    @Size(max = 500, message = "视频描述长度不能超过500个字符")
    private String description;

    /**
     * 可见范围：private-仅自己，couple-情侣可见，public-公开
     */
    @NotBlank(message = "可见范围不能为空")
    private String visibility;

    /**
     * 使用的滤镜名称
     */
    @Size(max = 50, message = "滤镜名称长度不能超过50个字符")
    private String filterName;

    /**
     * 背景音乐名称
     */
    @Size(max = 100, message = "背景音乐名称长度不能超过100个字符")
    private String musicName;

    /**
     * 视频标签列表
     */
    private List<String> tags;
}
