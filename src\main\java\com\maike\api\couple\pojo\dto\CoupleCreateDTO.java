package com.maike.api.couple.pojo.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * 创建情侣邀请请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleCreateDTO {

    /**
     * 情侣昵称
     */
    @NotBlank(message = "情侣昵称不能为空")
    @Size(min = 2, max = 100, message = "情侣昵称长度必须在2-100位之间")
    private String coupleName;

    /**
     * 纪念日日期
     */
    private LocalDate anniversaryDate;
}
