/**
 * API接口统一管理
 * 基于封装的request工具，提供所有后端接口的调用方法
 */

import request, { type ResponseData } from '@/utils/request'

// ==================== 用户认证相关接口 ====================

/**
 * 用户注册
 */
export interface UserRegisterParams {
  phone: string
  password: string
  nickname: string
  verifyCode: string
}

export interface UserRegisterResult {
  userId: string
  message: string
}

export const userRegister = (data: UserRegisterParams): Promise<ResponseData<UserRegisterResult>> => {
  return request.post('/user/register', data)
}

/**
 * 用户登录
 */
export interface UserLoginParams {
  phone: string
  password: string
}

export interface UserLoginResult {
  token: string
  userId: string
  nickname: string
  avatarUrl?: string
  coupleId?: string
}

export const userLogin = (data: UserLoginParams): Promise<ResponseData<UserLoginResult>> => {
  return request.post('/user/login', data)
}

/**
 * 获取用户信息
 */
export interface UserInfo {
  userId: string
  phone: string
  nickname: string
  avatarUrl?: string
  gender?: string
  birthday?: string
  signature?: string
  coupleId?: string
  createTime: string
}

export const getUserInfo = (): Promise<ResponseData<UserInfo>> => {
  return request.get('/user/info')
}

/**
 * 更新用户信息
 */
export interface UserUpdateParams {
  nickname?: string
  avatarUrl?: string
  gender?: string
  birthday?: string
  signature?: string
}

export interface UserUpdateResult {
  updated_fields: string[]
}

export const updateUserInfo = (data: UserUpdateParams): Promise<ResponseData<UserUpdateResult>> => {
  return request.put('/user/info', data)
}

/**
 * 发送验证码
 */
export interface SendCodeParams {
  phone: string
  type: 'register' | 'login' | 'reset_password'
}

export const sendVerifyCode = (data: SendCodeParams): Promise<ResponseData<any>> => {
  return request.post('/user/send-code', data)
}

/**
 * 用户退出登录
 */
export const userLogout = (): Promise<ResponseData<any>> => {
  return request.post('/user/logout')
}

// ==================== 情侣绑定相关接口 ====================

/**
 * 创建情侣邀请
 */
export interface CoupleCreateParams {
  coupleName?: string
  anniversaryDate?: string
}

export interface CoupleCreateResult {
  coupleId: string
  inviteCode: string
  expiresIn: number
}

export const createCoupleInvite = (data: CoupleCreateParams): Promise<ResponseData<CoupleCreateResult>> => {
  return request.post('/couple', data)
}

/**
 * 接受情侣邀请
 */
export interface CoupleAcceptParams {
  inviteCode: string
}

export const acceptCoupleInvite = (data: CoupleAcceptParams): Promise<ResponseData<any>> => {
  return request.post('/couple/accept', data)
}

/**
 * 获取情侣信息
 */
export interface CoupleInfo {
  coupleId: string
  coupleName: string
  anniversaryDate: string
  totalDays: number
  isActive: boolean
  partnerInfo: {
    userId: string
    nickname: string
    avatarUrl?: string
    gender?: string
  }
}

export const getCoupleInfo = (): Promise<ResponseData<CoupleInfo>> => {
  return request.get('/couple')
}

/**
 * 解除情侣关系
 */
export const unbindCouple = (): Promise<ResponseData<any>> => {
  return request.delete('/couple')
}

// ==================== 喝水打卡相关接口 ====================

/**
 * 添加喝水记录
 */
export interface WaterRecordCreateParams {
  amountMl: number
  cupType?: string
  temperature?: string
  note?: string
  location?: string
  recordedAt?: string
}

export interface WaterRecordCreateResult {
  recordId: string
  todayTotal: number
  goalProgress: number
  isGoalAchieved: boolean
  remainingAmount: number
}

export const addWaterRecord = (data: WaterRecordCreateParams): Promise<ResponseData<WaterRecordCreateResult>> => {
  return request.post('/water', data)
}

/**
 * 获取喝水记录列表
 */
export interface WaterRecord {
  id: string
  amountMl: number
  recordedAt: string
  cupType: string
  cupTypeText: string
  temperature: string
  temperatureText: string
  note?: string
  location?: string
  createTime: string
}

export interface WaterRecordListParams {
  date?: string
  page?: number
  size?: number
}

export const getWaterRecords = (params?: WaterRecordListParams): Promise<ResponseData<{
  records: WaterRecord[]
  total: number
  current: number
  size: number
}>> => {
  return request.get('/water', params)
}

/**
 * 设置每日目标
 */
export interface WaterGoalCreateParams {
  dailyGoalMl: number
  goalDate?: string
}

export interface WaterGoal {
  goalId: string
  dailyGoalMl: number
  goalDate: string
  actualAmountMl: number
  isAchieved: boolean
  completionRate: number
  remainingAmount: number
}

export const setDailyGoal = (data: WaterGoalCreateParams): Promise<ResponseData<WaterGoal>> => {
  return request.post('/water/goal', data)
}

/**
 * 获取每日目标
 */
export const getDailyGoal = (date?: string): Promise<ResponseData<WaterGoal>> => {
  return request.get('/water/goal', { date })
}

/**
 * 获取喝水统计
 */
export interface WaterStats {
  date: string
  totalAmount: number
  recordCount: number
}

export const getWaterStats = (startDate: string, endDate: string): Promise<ResponseData<WaterStats[]>> => {
  return request.get('/water/stats', { startDate, endDate })
}

// ==================== 视频功能相关接口 ====================

/**
 * 获取视频列表
 */
export interface Video {
  id: string
  videoUrl: string
  thumbnailUrl: string
  description: string
  visibility: string
  durationSeconds: number
  likeCount: number
  commentCount: number
  viewCount: number
  isLiked: boolean
  createTime: string
  authorInfo: {
    userId: string
    nickname: string
    avatarUrl?: string
  }
}

export interface VideoListParams {
  type: 'my' | 'couple' | 'public' | 'featured'
  page?: number
  size?: number
}

export const getVideoList = (params: VideoListParams): Promise<ResponseData<{
  videos: Video[]
  total: number
  current: number
  size: number
}>> => {
  return request.get('/video', params)
}

/**
 * 点赞/取消点赞视频
 */
export interface VideoLikeResult {
  isLiked: boolean
  likeCount: number
}

export const toggleVideoLike = (videoId: string): Promise<ResponseData<VideoLikeResult>> => {
  return request.post(`/video/${videoId}/like`)
}

// ==================== 成就系统相关接口 ====================

/**
 * 获取成就列表
 */
export interface Achievement {
  id: string
  name: string
  description: string
  iconUrl: string
  type: string
  category: string
  points: number
  rarity: string
  isUnlocked: boolean
  progress: number
  currentValue: number
  targetValue: number
}

export const getAchievements = (type?: string, category?: string): Promise<ResponseData<Achievement[]>> => {
  return request.get('/achievement', { type, category })
}

/**
 * 获取用户成就统计
 */
export interface AchievementStats {
  totalAchievements: number
  unlockedCount: number
  totalPoints: number
  unlockRate: number
}

export const getAchievementStats = (): Promise<ResponseData<AchievementStats>> => {
  return request.get('/achievement/stats')
}

// ==================== 通知提醒相关接口 ====================

/**
 * 获取通知列表
 */
export interface Notification {
  id: string
  type: string
  title: string
  message: string
  isRead: boolean
  createTime: string
}

export interface NotificationListParams {
  type?: string
  isRead?: boolean
  page?: number
  size?: number
}

export const getNotifications = (params?: NotificationListParams): Promise<ResponseData<{
  notifications: Notification[]
  total: number
  unreadCount: number
}>> => {
  return request.get('/notification', params)
}

/**
 * 标记通知为已读
 */
export const markNotificationAsRead = (notificationId: string): Promise<ResponseData<any>> => {
  return request.put(`/notification/${notificationId}/read`)
}

/**
 * 标记所有通知为已读
 */
export const markAllNotificationsAsRead = (): Promise<ResponseData<any>> => {
  return request.put('/notification/read-all')
}
