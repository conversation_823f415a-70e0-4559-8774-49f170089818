/**
 * API接口统一管理
 * 基于封装的request工具，提供所有后端接口的调用方法
 */

import request, { type ResponseData } from '@/utils/request'

// ==================== 用户认证相关接口 ====================

/**
 * 用户注册
 */
export interface UserRegisterParams {
  phone_number: string
  password: string
  nickname: string
  verification_code: string
  gender?: string
  birthday?: string
}

export interface UserRegisterResult {
  user_id: string
  token: string
  expires_in: number
}

export const userRegister = (data: UserRegisterParams): Promise<ResponseData<UserRegisterResult>> => {
  return request.post('/user', data)
}

/**
 * 用户登录
 */
export interface UserLoginParams {
  phone_number: string
  password: string
}

export interface UserLoginResult {
  user_id: string
  token: string
  expires_in: number
  user_info: {
    nickname: string
    avatar_url?: string
    gender?: string
    couple_id?: string
  }
}

export const userLogin = (data: UserLoginParams): Promise<ResponseData<UserLoginResult>> => {
  return request.post('/user/login', data)
}

/**
 * 获取用户信息
 */
export interface UserInfo {
  id: string
  phone_number: string
  nickname: string
  avatar_url?: string
  gender?: string
  birthday?: string
  bio?: string
  couple_id?: string
  create_time: string
}

export const getUserInfo = (): Promise<ResponseData<UserInfo>> => {
  return request.get('/user')
}

/**
 * 更新用户信息
 */
export interface UserUpdateParams {
  nickname?: string
  avatar_url?: string
  gender?: string
  birthday?: string
  bio?: string
}

export interface UserUpdateResult {
  updated_fields: string[]
}

export const updateUserInfo = (data: UserUpdateParams): Promise<ResponseData<UserUpdateResult>> => {
  return request.put('/user', data)
}

/**
 * 发送验证码
 */
export interface SendCodeParams {
  phone_number: string
  type: 'register' | 'login' | 'reset_password'
}

export const sendVerifyCode = (data: SendCodeParams): Promise<ResponseData<{ expires_in: number }>> => {
  return request.post('/user/sendCode', data)
}

/**
 * 用户退出登录
 */
export const userLogout = (): Promise<ResponseData<any>> => {
  return request.delete('/user/logout')
}

/**
 * 修改密码
 */
export interface ChangePasswordParams {
  old_password: string
  new_password: string
  verification_code: string
}

export const changePassword = (data: ChangePasswordParams): Promise<ResponseData<any>> => {
  return request.put('/user/password', data)
}

/**
 * 删除账户
 */
export interface DeleteAccountParams {
  password: string
  verification_code: string
}

export const deleteAccount = (data: DeleteAccountParams): Promise<ResponseData<any>> => {
  return request.delete('/user', { data })
}

// ==================== 情侣绑定相关接口 ====================

/**
 * 创建情侣邀请
 */
export interface CoupleCreateParams {
  couple_name?: string
  anniversary_date?: string
}

export interface CoupleCreateResult {
  couple_id: string
  invite_code: string
  expires_in: number
}

export const createCoupleInvite = (data: CoupleCreateParams): Promise<ResponseData<CoupleCreateResult>> => {
  return request.post('/couple', data)
}

/**
 * 接受情侣邀请
 */
export interface CoupleAcceptParams {
  invite_code: string
}

export interface CoupleAcceptResult {
  couple_id: string
  partner_info: {
    user_id: string
    nickname: string
    avatar_url?: string
  }
  bound_at: string
}

export const acceptCoupleInvite = (data: CoupleAcceptParams): Promise<ResponseData<CoupleAcceptResult>> => {
  return request.post('/couple/accept', data)
}

/**
 * 获取情侣信息
 */
export interface CoupleInfo {
  couple_id: string
  couple_name: string
  anniversary_date: string
  total_days: number
  is_active: boolean
  bound_at: string
  partner_info: {
    user_id: string
    nickname: string
    avatar_url?: string
    gender?: string
  }
}

export const getCoupleInfo = (): Promise<ResponseData<CoupleInfo>> => {
  return request.get('/couple')
}

/**
 * 更新情侣信息
 */
export interface CoupleUpdateParams {
  couple_name?: string
  anniversary_date?: string
}

export const updateCoupleInfo = (data: CoupleUpdateParams): Promise<ResponseData<{ updated_fields: string[] }>> => {
  return request.put('/couple', data)
}

/**
 * 解除情侣关系
 */
export interface UnbindCoupleParams {
  reason?: string
  password: string
}

export const unbindCouple = (data: UnbindCoupleParams): Promise<ResponseData<any>> => {
  return request.delete('/couple', { data })
}

/**
 * 发送情侣提醒
 */
export interface CoupleRemindParams {
  message: string
  type: string
}

export const sendCoupleRemind = (data: CoupleRemindParams): Promise<ResponseData<{ notification_id: string }>> => {
  return request.post('/couple/remind', data)
}

// ==================== 喝水打卡相关接口 ====================

/**
 * 添加喝水记录
 */
export interface WaterRecordCreateParams {
  amount_ml: number
  cup_type?: string
  temperature?: string
  note?: string
  location?: string
  recorded_at?: string
}

export interface WaterRecordCreateResult {
  record_id: string
  today_total: number
  goal_progress: number
  is_goal_achieved: boolean
}

export const addWaterRecord = (data: WaterRecordCreateParams): Promise<ResponseData<WaterRecordCreateResult>> => {
  return request.post('/water', data)
}

/**
 * 获取喝水记录列表
 */
export interface WaterRecord {
  id: string
  amount_ml: number
  recorded_at: string
  cup_type: string
  temperature: string
  note?: string
  location?: string
}

export interface WaterRecordListParams {
  date?: string
  page?: number
  size?: number
}

export const getWaterRecords = (params?: WaterRecordListParams): Promise<ResponseData<{
  total: number
  page: number
  size: number
  records: WaterRecord[]
}>> => {
  return request.get('/water', params)
}

/**
 * 更新喝水记录
 */
export interface WaterRecordUpdateParams {
  amount_ml?: number
  note?: string
}

export const updateWaterRecord = (recordId: string, data: WaterRecordUpdateParams): Promise<ResponseData<{ updated_fields: string[] }>> => {
  return request.put(`/water/${recordId}`, data)
}

/**
 * 删除喝水记录
 */
export const deleteWaterRecord = (recordId: string): Promise<ResponseData<any>> => {
  return request.delete(`/water/${recordId}`)
}

/**
 * 设置每日目标
 */
export interface WaterGoalCreateParams {
  daily_goal_ml: number
  goal_date?: string
}

export interface WaterGoal {
  goal_id: string
  daily_goal_ml: number
  goal_date: string
  actual_amount_ml: number
  is_achieved: boolean
  completion_rate: number
}

export const setDailyGoal = (data: WaterGoalCreateParams): Promise<ResponseData<{ goal_id: string; daily_goal_ml: number }>> => {
  return request.post('/water/goal', data)
}

/**
 * 获取每日目标
 */
export const getDailyGoal = (date?: string): Promise<ResponseData<WaterGoal>> => {
  return request.get('/water/goal', { date })
}

/**
 * 获取喝水统计
 */
export interface WaterStats {
  type: string
  period: string
  total_amount: number
  avg_daily_amount: number
  goal_achieved_days: number
  total_days: number
  achievement_rate: number
  daily_data: Array<{
    date: string
    amount: number
    goal: number
    achieved: boolean
  }>
}

export const getWaterStats = (type: 'daily' | 'weekly' | 'monthly', start_date?: string, end_date?: string): Promise<ResponseData<WaterStats>> => {
  return request.get('/water/statistics', { type, start_date, end_date })
}

/**
 * 获取今日进度
 */
export interface TodayProgress {
  today_total: number
  daily_goal: number
  completion_rate: number
  remaining: number
  is_achieved: boolean
  records_count: number
  last_record_time?: string
  next_reminder?: string
}

export const getTodayProgress = (): Promise<ResponseData<TodayProgress>> => {
  return request.get('/water/todayProgress')
}

// ==================== 视频功能相关接口 ====================

/**
 * 获取视频列表
 */
export interface Video {
  id: string
  user_id: string
  user_info: {
    nickname: string
    avatar_url?: string
  }
  video_url: string
  thumbnail_url: string
  description: string
  visibility: string
  duration_seconds: number
  like_count: number
  comment_count: number
  view_count: number
  is_liked: boolean
  create_time: string
}

export interface VideoListParams {
  type: 'my' | 'couple' | 'public'
  page?: number
  size?: number
}

export const getVideoList = (params: VideoListParams): Promise<ResponseData<{
  total: number
  page: number
  size: number
  videos: Video[]
}>> => {
  return request.get('/video', params)
}

/**
 * 点赞/取消点赞视频
 */
export interface VideoLikeResult {
  is_liked: boolean
  like_count: number
}

export const toggleVideoLike = (videoId: string): Promise<ResponseData<VideoLikeResult>> => {
  return request.post(`/video/${videoId}/like`)
}

/**
 * 上传视频
 */
export interface VideoUploadParams {
  video_file: string // 文件路径
  thumbnail_file?: string
  description: string
  visibility: 'private' | 'couple' | 'public'
  filter_name?: string
  music_name?: string
  tags?: string
}

export interface VideoUploadResult {
  video_id: string
  video_url: string
  thumbnail_url: string
  duration_seconds: number
  status: string
}

export const uploadVideo = (data: VideoUploadParams): Promise<ResponseData<VideoUploadResult>> => {
  // 这里需要使用 request.upload 方法
  return request.upload('/video', data.video_file, 'video_file', {
    thumbnail_file: data.thumbnail_file,
    description: data.description,
    visibility: data.visibility,
    filter_name: data.filter_name,
    music_name: data.music_name,
    tags: data.tags
  })
}

/**
 * 获取视频详情
 */
export interface VideoDetail extends Video {
  filter_name?: string
  music_name?: string
  tags?: string[]
}

export const getVideoDetail = (videoId: string): Promise<ResponseData<VideoDetail>> => {
  return request.get(`/video/${videoId}`)
}

/**
 * 评论视频
 */
export interface VideoCommentParams {
  comment_text: string
  reply_to_id?: string
  emoji_type?: string
}

export const commentVideo = (videoId: string, data: VideoCommentParams): Promise<ResponseData<{ comment_id: string; comment_count: number }>> => {
  return request.post(`/video/${videoId}/comment`, data)
}

/**
 * 获取视频评论
 */
export interface VideoComment {
  id: string
  user_id: string
  user_info: {
    nickname: string
    avatar_url?: string
  }
  comment_text: string
  emoji_type?: string
  reply_to_id?: string
  create_time: string
}

export const getVideoComments = (videoId: string, page?: number, size?: number): Promise<ResponseData<{
  total: number
  page: number
  size: number
  comments: VideoComment[]
}>> => {
  return request.get(`/video/${videoId}/comment`, { page, size })
}

/**
 * 删除视频
 */
export const deleteVideo = (videoId: string): Promise<ResponseData<any>> => {
  return request.delete(`/video/${videoId}`)
}

/**
 * 举报视频
 */
export interface VideoReportParams {
  reason: string
  description?: string
}

export const reportVideo = (videoId: string, data: VideoReportParams): Promise<ResponseData<{ report_id: string }>> => {
  return request.post(`/video/${videoId}/report`, data)
}

// ==================== 成就系统相关接口 ====================

/**
 * 获取成就列表
 */
export interface Achievement {
  id: string
  name: string
  description: string
  icon_url: string
  type: string
  category: string
  points: number
  rarity: string
  is_unlocked: boolean
  unlocked_at?: string
  progress: number
}

export const getAchievements = (type?: string, category?: string): Promise<ResponseData<{ achievements: Achievement[] }>> => {
  return request.get('/achievement', { type, category })
}

/**
 * 获取用户成就统计
 */
export interface AchievementStats {
  total_achievements: number
  unlocked_count: number
  total_points: number
  recent_unlocked: Array<{
    achievement_id: string
    name: string
    unlocked_at: string
  }>
}

export const getAchievementStats = (): Promise<ResponseData<AchievementStats>> => {
  return request.get('/achievement/my')
}

// ==================== 通知提醒相关接口 ====================

/**
 * 获取通知列表
 */
export interface Notification {
  id: string
  type: string
  title: string
  message: string
  is_read: boolean
  create_time: string
}

export interface NotificationListParams {
  type?: string
  is_read?: boolean
  page?: number
  size?: number
}

export const getNotifications = (params?: NotificationListParams): Promise<ResponseData<{
  unread_count: number
  notifications: Notification[]
}>> => {
  return request.get('/notification', params)
}

/**
 * 标记通知为已读
 */
export const markNotificationAsRead = (notificationId: string): Promise<ResponseData<any>> => {
  return request.put(`/notification/${notificationId}/read`)
}

/**
 * 标记所有通知为已读
 */
export const markAllNotificationsAsRead = (): Promise<ResponseData<any>> => {
  return request.put('/notification/read-all')
}

// ==================== 挑战功能相关接口 ====================

/**
 * 创建挑战
 */
export interface ChallengeCreateParams {
  name: string
  description: string
  type: string
  duration_days: number
  requirements: {
    daily_goal: number
    consecutive_days: number
  }
  start_date: string
}

export const createChallenge = (data: ChallengeCreateParams): Promise<ResponseData<any>> => {
  return request.post('/challenge', data)
}

/**
 * 获取挑战列表
 */
export interface Challenge {
  id: string
  name: string
  status: string
  start_date: string
  end_date: string
  user1_progress: number
  user2_progress: number
  total_progress: number
}

export const getChallengeList = (): Promise<ResponseData<{ challenges: Challenge[] }>> => {
  return request.get('/challenge')
}

// ==================== 提醒设置相关接口 ====================

/**
 * 设置提醒
 */
export interface ReminderCreateParams {
  reminder_time: string
  message: string
  repeat_days: string
  sound_type?: string
}

export const setReminder = (data: ReminderCreateParams): Promise<ResponseData<{ reminder_id: string }>> => {
  return request.post('/reminder', data)
}
