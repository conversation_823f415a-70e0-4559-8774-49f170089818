<template>
  <view class="register-page">
    <!-- 顶部装饰 -->
    <view class="header-decoration">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">嘉依打卡</text>
      <text class="app-slogan">元气水杯与Ta的约定</text>
    </view>

    <!-- 注册表单 -->
    <view class="register-form">
      <view class="form-title">
        <text>创建账户</text>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/phone.png"></image>
          <input 
            class="form-input" 
            type="number" 
            v-model="registerForm.phone_number" 
            placeholder="请输入手机号"
            maxlength="11"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/code.png"></image>
          <input 
            class="form-input" 
            type="number" 
            v-model="registerForm.verification_code" 
            placeholder="请输入验证码"
            maxlength="6"
          />
          <button 
            class="code-btn" 
            @click="sendCode" 
            :disabled="codeCountdown > 0"
          >
            {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
          </button>
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/password.png"></image>
          <input 
            class="form-input" 
            :type="showPassword ? 'text' : 'password'" 
            v-model="registerForm.password" 
            placeholder="请设置密码（6-20位）"
          />
          <image 
            class="toggle-password" 
            :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
            @click="showPassword = !showPassword"
          ></image>
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/password.png"></image>
          <input 
            class="form-input" 
            :type="showConfirmPassword ? 'text' : 'password'" 
            v-model="registerForm.confirmPassword" 
            placeholder="请确认密码"
          />
          <image 
            class="toggle-password" 
            :src="showConfirmPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
            @click="showConfirmPassword = !showConfirmPassword"
          ></image>
        </view>
      </view>
      
      <view class="form-item">
        <view class="input-wrapper">
          <image class="input-icon" src="/static/icons/user.png"></image>
          <input 
            class="form-input" 
            v-model="registerForm.nickname" 
            placeholder="请输入昵称"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="gender-section">
          <text class="gender-label">性别</text>
          <view class="gender-options">
            <view 
              class="gender-option" 
              :class="{ active: registerForm.gender === 'female' }"
              @click="registerForm.gender = 'female'"
            >
              <image class="gender-icon" src="/static/icons/female.png"></image>
              <text>女生</text>
            </view>
            <view 
              class="gender-option" 
              :class="{ active: registerForm.gender === 'male' }"
              @click="registerForm.gender = 'male'"
            >
              <image class="gender-icon" src="/static/icons/male.png"></image>
              <text>男生</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <view class="birthday-section">
          <text class="birthday-label">生日</text>
          <picker mode="date" :value="registerForm.birthday" @change="onBirthdayChange">
            <view class="birthday-picker">
              <text>{{ registerForm.birthday || '选择生日' }}</text>
              <image class="picker-icon" src="/static/icons/calendar.png"></image>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="agreement-section">
        <view class="agreement-check" @click="toggleAgreement">
          <image 
            class="check-icon" 
            :src="agreedToTerms ? '/static/icons/check-filled.png' : '/static/icons/check-empty.png'"
          ></image>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" @click.stop="showUserAgreement">《用户协议》</text>
          <text class="agreement-text">和</text>
          <text class="agreement-link" @click.stop="showPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
      
      <button class="register-btn" @click="handleRegister" :disabled="!canRegister">
        注册
      </button>
      
      <view class="login-link">
        <text>已有账号？</text>
        <text class="link-text" @click="goToLogin">立即登录</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import request from '@/utils/request'

// 响应式数据
const registerForm = ref({
  phone_number: '',
  verification_code: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  gender: 'female',
  birthday: ''
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const agreedToTerms = ref(false)
const codeCountdown = ref(0)

// 计算属性：是否可以注册
const canRegister = computed(() => {
  return registerForm.value.phone_number.length === 11 && 
         registerForm.value.verification_code.length === 6 &&
         registerForm.value.password.length >= 6 && 
         registerForm.value.password === registerForm.value.confirmPassword &&
         registerForm.value.nickname.trim() &&
         agreedToTerms.value
})

// 发送验证码
const sendCode = async () => {
  if (!registerForm.value.phone_number || registerForm.value.phone_number.length !== 11) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }
  
  try {
    await request.post('/user/sendCode', {
      phone_number: registerForm.value.phone_number,
      type: 'register'
    })
    
    // 开始倒计时
    codeCountdown.value = 60
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
  } catch (error) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'error'
    })
  }
}

// 注册处理
const handleRegister = async () => {
  if (!canRegister.value) {
    uni.showToast({
      title: '请检查输入信息',
      icon: 'none'
    })
    return
  }
  
  try {
    uni.showLoading({ title: '注册中...' })
    
    const response = await request.post('/user', {
      phone_number: registerForm.value.phone_number,
      password: registerForm.value.password,
      verification_code: registerForm.value.verification_code,
      nickname: registerForm.value.nickname,
      gender: registerForm.value.gender,
      birthday: registerForm.value.birthday
    })
    
    // 保存token和用户信息
    request.setToken(response.data.token)
    uni.setStorageSync('userInfo', response.data.user_info)
    
    uni.hideLoading()
    uni.showToast({
      title: '注册成功',
      icon: 'success'
    })
    
    // 跳转到首页
    setTimeout(() => {
      uni.switchTab({ url: '/pages/index/index' })
    }, 1500)
    
  } catch (error) {
    uni.hideLoading()
    console.error('注册失败:', error)
    uni.showToast({
      title: '注册失败，请重试',
      icon: 'error'
    })
  }
}

// 生日选择
const onBirthdayChange = (e: any) => {
  registerForm.value.birthday = e.detail.value
}

// 同意协议
const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value
}

// 页面跳转方法
const goToLogin = () => {
  uni.navigateBack()
}

const showUserAgreement = () => {
  uni.navigateTo({ url: '/pages/legal/user-agreement' })
}

const showPrivacyPolicy = () => {
  uni.navigateTo({ url: '/pages/legal/privacy-policy' })
}
</script>

<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFF0F5 50%, #FFE4E1 100%);
  padding: 0 48rpx;
  display: flex;
  flex-direction: column;
}

/* 顶部装饰样式 */
.header-decoration {
  text-align: center;
  padding: 80rpx 0 60rpx;
}

.logo {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 12rpx;
}

.app-slogan {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 注册表单样式 */
.register-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 16rpx 64rpx rgba(255, 182, 193, 0.2);
}

.form-title {
  text-align: center;
  margin-bottom: 40rpx;
}

.form-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.form-item {
  margin-bottom: 28rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #FFF0F5;
  border-radius: 48rpx;
  padding: 0 32rpx;
  border: 2rpx solid #FFE4E1;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #FF69B4;
  box-shadow: 0 0 0 4rpx rgba(255, 105, 180, 0.1);
}

.input-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
}

.toggle-password {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

.code-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 16rpx;
}

.code-btn:disabled {
  background: #E0E0E0;
  color: #999;
}

/* 性别选择样式 */
.gender-section {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.gender-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  min-width: 80rpx;
}

.gender-options {
  display: flex;
  gap: 24rpx;
  flex: 1;
}

.gender-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #FFF0F5;
  border-radius: 20rpx;
  border: 2rpx solid #FFE4E1;
  flex: 1;
  transition: all 0.3s ease;
}

.gender-option.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border-color: #FF69B4;
}

.gender-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
}

.gender-option text {
  font-size: 24rpx;
  font-weight: bold;
}

/* 生日选择样式 */
.birthday-section {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.birthday-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  min-width: 80rpx;
}

.birthday-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFF0F5;
  border-radius: 24rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #FFE4E1;
  flex: 1;
}

.picker-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 协议样式 */
.agreement-section {
  margin-bottom: 32rpx;
}

.agreement-check {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
}

.agreement-link {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

.register-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
  margin-bottom: 32rpx;
}

.register-btn:disabled {
  background: #E0E0E0;
  color: #999;
  box-shadow: none;
}

.register-btn:active:not(:disabled) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.4);
}

.login-link {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.link-text {
  color: #FF69B4;
  font-weight: bold;
  margin-left: 8rpx;
}
</style>
