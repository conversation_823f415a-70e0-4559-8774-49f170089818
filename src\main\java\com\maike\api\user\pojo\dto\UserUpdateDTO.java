package com.maike.api.user.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 用户信息更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class UserUpdateDTO {

    /**
     * 用户昵称
     */
    @Size(min = 2, max = 20, message = "昵称长度必须在2-20位之间")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,20}$", message = "昵称只能包含中文、字母、数字")
    private String nickname;

    /**
     * 头像URL
     */
    @Size(max = 500, message = "头像URL长度不能超过500字符")
    private String avatarUrl;

    /**
     * 性别：male-男性，female-女性，unknown-未知
     */
    @Pattern(regexp = "^(male|female|unknown)$", message = "性别参数不正确")
    private String gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 个性签名
     */
    @Size(max = 200, message = "个性签名长度不能超过200字符")
    private String bio;
}
