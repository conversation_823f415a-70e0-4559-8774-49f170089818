<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maike.api.couple.mapper.CoupleMapper">

    <!-- 情侣关系基础字段 -->
    <sql id="Base_Column_List">
        id, invite_code, user1_id, user2_id, bound_at, is_active, anniversary_date,
        couple_name, total_days, status, create_by, create_time, update_by, update_time, remark
    </sql>

    <!-- 根据邀请码查询情侣关系 -->
    <select id="selectByInviteCode" resultType="com.maike.api.couple.pojo.entity.Couple">
        SELECT
        <include refid="Base_Column_List"/>
        FROM couples
        WHERE invite_code = #{inviteCode}
        AND status != '2'
        LIMIT 1
    </select>

    <!-- 根据用户ID查询情侣关系 -->
    <select id="selectByUserId" resultType="com.maike.api.couple.pojo.entity.Couple">
        SELECT
        <include refid="Base_Column_List"/>
        FROM couples
        WHERE (user1_id = #{userId} OR user2_id = #{userId})
        AND status != '2'
        AND is_active = 1
        LIMIT 1
    </select>

    <!-- 检查邀请码是否存在 -->
    <select id="countByInviteCode" resultType="int">
        SELECT COUNT(1)
        FROM couples
        WHERE invite_code = #{inviteCode}
        AND status != '2'
    </select>

    <!-- 更新情侣关系绑定信息 -->
    <update id="updateBoundInfo">
        UPDATE couples
        SET user2_id = #{user2Id},
            bound_at = NOW(),
            is_active = 1,
            update_time = NOW()
        WHERE id = #{coupleId}
        AND status != '2'
    </update>

    <!-- 解除情侣关系 -->
    <update id="unbindCouple">
        UPDATE couples
        SET is_active = 0,
            update_time = NOW()
        WHERE id = #{coupleId}
        AND status != '2'
    </update>

    <!-- 更新总天数 -->
    <update id="updateTotalDays">
        UPDATE couples
        SET total_days = #{totalDays},
            update_time = NOW()
        WHERE id = #{coupleId}
        AND status != '2'
    </update>

</mapper>
