package com.maike.api.challenge.pojo.dto;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.time.LocalDate;

/**
 * 创建挑战请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ChallengeCreateDTO {

    /**
     * 挑战名称
     */
    @NotBlank(message = "挑战名称不能为空")
    @Size(max = 100, message = "挑战名称长度不能超过100个字符")
    private String name;

    /**
     * 挑战描述
     */
    @Size(max = 500, message = "挑战描述长度不能超过500个字符")
    private String description;

    /**
     * 挑战类型：water-喝水挑战，video-视频挑战，interaction-互动挑战，custom-自定义
     */
    @NotBlank(message = "挑战类型不能为空")
    @Pattern(regexp = "^(water|video|interaction|custom)$", message = "挑战类型必须是water、video、interaction或custom")
    private String type;

    /**
     * 挑战持续天数
     */
    @NotNull(message = "挑战持续天数不能为空")
    @Min(value = 1, message = "挑战持续天数不能小于1天")
    @Max(value = 365, message = "挑战持续天数不能超过365天")
    private Integer durationDays;

    /**
     * 挑战开始日期
     */
    @NotNull(message = "挑战开始日期不能为空")
    @Future(message = "挑战开始日期必须是未来日期")
    private LocalDate startDate;

    /**
     * 挑战要求，JSON格式
     */
    private String requirements;

    /**
     * 奖励类型：achievement-成就，theme-主题，title-称号
     */
    @Pattern(regexp = "^(achievement|theme|title)$", message = "奖励类型必须是achievement、theme或title")
    private String rewardType;

    /**
     * 奖励内容
     */
    @Size(max = 200, message = "奖励内容长度不能超过200个字符")
    private String rewardValue;

    /**
     * 是否公开：true-公开，false-私有
     */
    private Boolean isPublic;
}
