package com.maike.api.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.user.pojo.dto.*;
import com.maike.api.user.pojo.entity.User;
import com.maike.api.user.pojo.vo.LoginVO;
import com.maike.api.user.pojo.vo.RegisterVO;
import com.maike.api.user.pojo.vo.UserInfoVO;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface UserService extends IService<User> {

    /**
     * 用户注册
     * 
     * @param registerDTO 注册信息
     * @param clientIp    客户端IP
     * @return 注册结果
     */
    RegisterVO register(UserRegisterDTO registerDTO, String clientIp);

    /**
     * 用户登录
     * 
     * @param loginDTO 登录信息
     * @param clientIp 客户端IP
     * @return 登录结果
     */
    LoginVO login(UserLoginDTO loginDTO, String clientIp);

    /**
     * 获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoVO getUserInfo(String userId);

    /**
     * 更新用户信息
     * 
     * @param userId    用户ID
     * @param updateDTO 更新信息
     * @return 更新的字段列表
     */
    List<String> updateUserInfo(String userId, UserUpdateDTO updateDTO);

    /**
     * 修改密码
     * 
     * @param userId          用户ID
     * @param passwordChangeDTO 密码修改信息
     */
    void changePassword(String userId, PasswordChangeDTO passwordChangeDTO);

    /**
     * 发送验证码
     * 
     * @param sendCodeDTO 发送验证码信息
     * @return 验证码过期时间（秒）
     */
    Integer sendVerificationCode(SendCodeDTO sendCodeDTO);

    /**
     * 退出登录
     * 
     * @param userId 用户ID
     * @param token  JWT令牌
     */
    void logout(String userId, String token);

    /**
     * 删除账户
     * 
     * @param userId    用户ID
     * @param deleteDTO 删除账户信息
     */
    void deleteAccount(String userId, UserDeleteDTO deleteDTO);

    /**
     * 根据手机号查询用户
     * 
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    User getUserByPhoneNumber(String phoneNumber);

    /**
     * 验证验证码
     * 
     * @param phoneNumber 手机号
     * @param code        验证码
     * @param type        验证码类型
     * @return true-验证通过，false-验证失败
     */
    boolean verifyCode(String phoneNumber, String code, String type);

    /**
     * 检查昵称是否可用
     * 
     * @param nickname      昵称
     * @param excludeUserId 排除的用户ID
     * @return true-可用，false-不可用
     */
    boolean isNicknameAvailable(String nickname, String excludeUserId);

    /**
     * 更新用户最后登录信息
     * 
     * @param userId  用户ID
     * @param loginIp 登录IP
     */
    void updateLoginInfo(String userId, String loginIp);

    /**
     * 根据用户ID列表批量查询用户信息
     * 
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    List<User> getUsersByIds(List<String> userIds);

    /**
     * 检查用户是否存在且状态正常
     * 
     * @param userId 用户ID
     * @return true-存在且正常，false-不存在或状态异常
     */
    boolean isUserExistAndNormal(String userId);
}
