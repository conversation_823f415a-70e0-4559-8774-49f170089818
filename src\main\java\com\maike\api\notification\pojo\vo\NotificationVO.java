package com.maike.api.notification.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class NotificationVO {

    /**
     * 通知ID
     */
    private String id;

    /**
     * 通知类型：system-系统通知，achievement-成就通知，reminder-提醒通知，interaction-互动通知，challenge-挑战通知，couple-情侣通知
     */
    private String type;

    /**
     * 通知类型显示文本
     */
    private String typeText;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String message;

    /**
     * 附加数据，JSON格式
     */
    private String data;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 阅读时间
     */
    private LocalDateTime readAt;

    /**
     * 优先级：low-低，normal-普通，high-高，urgent-紧急
     */
    private String priority;

    /**
     * 优先级显示文本
     */
    private String priorityText;

    /**
     * 优先级颜色
     */
    private String priorityColor;

    /**
     * 操作类型：click-点击，redirect-跳转，none-无操作
     */
    private String actionType;

    /**
     * 操作链接
     */
    private String actionUrl;

    /**
     * 发送者ID
     */
    private String senderId;

    /**
     * 关联对象ID
     */
    private String relatedId;

    /**
     * 关联对象类型
     */
    private String relatedType;

    /**
     * 过期时间
     */
    private LocalDateTime expireAt;

    /**
     * 是否推送
     */
    private Boolean isPush;

    /**
     * 推送时间
     */
    private LocalDateTime pushAt;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发送者信息
     */
    private SenderInfo senderInfo;

    /**
     * 发送者信息内部类
     */
    @Data
    public static class SenderInfo {
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 头像URL
         */
        private String avatarUrl;

        /**
         * 性别
         */
        private String gender;
    }
}
