<template>
  <view class="couple-page">
    <!-- 未绑定状态 -->
    <view class="no-couple-section" v-if="!coupleInfo.couple_id">
      <view class="empty-illustration">
        <image class="empty-image" src="/static/illustrations/couple-empty.png" mode="aspectFit"></image>
        <text class="empty-title">还没有绑定情侣关系</text>
        <text class="empty-desc">快来邀请你的TA一起喝水打卡吧！</text>
      </view>
      
      <view class="bind-actions">
        <button class="create-invite-btn" @click="createInvite">创建邀请</button>
        <button class="join-invite-btn" @click="showJoinModal">加入邀请</button>
      </view>
    </view>

    <!-- 已绑定状态 -->
    <view class="couple-content" v-else>
      <!-- 情侣信息卡片 -->
      <view class="couple-info-card">
        <view class="couple-header">
          <view class="user-avatar-section">
            <image class="user-avatar" :src="userInfo.avatar_url" mode="aspectFill"></image>
            <text class="user-name">{{ userInfo.nickname }}</text>
          </view>
          
          <view class="love-center">
            <image class="heart-icon" src="/static/icons/heart-filled.png"></image>
            <text class="together-days">{{ coupleInfo.total_days }}天</text>
          </view>
          
          <view class="partner-avatar-section">
            <image class="partner-avatar" :src="coupleInfo.partner_info.avatar_url" mode="aspectFill"></image>
            <text class="partner-name">{{ coupleInfo.partner_info.nickname }}</text>
          </view>
        </view>
        
        <view class="couple-name">
          <text>{{ coupleInfo.couple_name }}</text>
        </view>
        
        <view class="anniversary">
          <text>纪念日：{{ formatDate(coupleInfo.anniversary_date) }}</text>
        </view>
      </view>

      <!-- 今日进度对比 -->
      <view class="progress-comparison">
        <view class="section-title">
          <text>今日喝水进度</text>
        </view>
        
        <view class="progress-bars">
          <view class="user-progress">
            <view class="progress-header">
              <image class="mini-avatar" :src="userInfo.avatar_url" mode="aspectFill"></image>
              <text class="progress-name">{{ userInfo.nickname }}</text>
              <text class="progress-amount">{{ userProgress.today_total }}ml</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: userProgress.completion_rate + '%' }"></view>
            </view>
          </view>
          
          <view class="partner-progress">
            <view class="progress-header">
              <image class="mini-avatar" :src="coupleInfo.partner_info.avatar_url" mode="aspectFill"></image>
              <text class="progress-name">{{ coupleInfo.partner_info.nickname }}</text>
              <text class="progress-amount">{{ partnerProgress.today_total }}ml</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: partnerProgress.completion_rate + '%' }"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 互动功能 -->
      <view class="interaction-section">
        <view class="section-title">
          <text>互动功能</text>
        </view>
        
        <view class="interaction-buttons">
          <view class="interaction-btn" @click="sendReminder">
            <image class="btn-icon" src="/static/icons/bell.png"></image>
            <text>提醒喝水</text>
          </view>
          
          <view class="interaction-btn" @click="sendEncouragement">
            <image class="btn-icon" src="/static/icons/cheer.png"></image>
            <text>加油鼓励</text>
          </view>
          
          <view class="interaction-btn" @click="createChallenge">
            <image class="btn-icon" src="/static/icons/challenge.png"></image>
            <text>发起挑战</text>
          </view>
          
          <view class="interaction-btn" @click="sendGift">
            <image class="btn-icon" src="/static/icons/gift.png"></image>
            <text>送小礼物</text>
          </view>
        </view>
      </view>

      <!-- 共同成就 -->
      <view class="shared-achievements">
        <view class="section-title">
          <text>共同成就</text>
          <text class="more-btn" @click="viewAllAchievements">查看全部</text>
        </view>
        
        <view class="achievement-list">
          <view class="achievement-item" v-for="achievement in sharedAchievements" :key="achievement.id">
            <image class="achievement-icon" :src="achievement.icon_url" mode="aspectFit"></image>
            <view class="achievement-info">
              <text class="achievement-name">{{ achievement.name }}</text>
              <text class="achievement-desc">{{ achievement.description }}</text>
            </view>
            <text class="achievement-date">{{ formatDate(achievement.unlocked_at) }}</text>
          </view>
        </view>
      </view>

      <!-- 设置按钮 -->
      <view class="couple-settings">
        <button class="settings-btn" @click="showCoupleSettings">情侣设置</button>
      </view>
    </view>

    <!-- 加入邀请弹窗 -->
    <uni-popup ref="joinPopup" type="center">
      <view class="join-popup">
        <view class="popup-header">
          <text class="popup-title">加入邀请</text>
        </view>
        <view class="popup-content">
          <view class="input-group">
            <input 
              class="invite-code-input" 
              v-model="inviteCode" 
              placeholder="请输入邀请码"
              maxlength="8"
            />
          </view>
        </view>
        <view class="popup-actions">
          <button class="cancel-btn" @click="closeJoinModal">取消</button>
          <button class="confirm-btn" @click="joinCouple">确认</button>
        </view>
      </view>
    </uni-popup>

    <!-- 创建邀请成功弹窗 -->
    <uni-popup ref="inviteSuccessPopup" type="center">
      <view class="invite-success-popup">
        <view class="popup-header">
          <text class="popup-title">邀请码已生成</text>
        </view>
        <view class="popup-content">
          <view class="invite-code-display">
            <text class="invite-code">{{ generatedInviteCode }}</text>
          </view>
          <text class="invite-tips">请将邀请码分享给你的TA</text>
        </view>
        <view class="popup-actions">
          <button class="copy-btn" @click="copyInviteCode">复制邀请码</button>
          <button class="share-btn" @click="shareInviteCode">分享</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

// 响应式数据
const userInfo = ref({
  nickname: '',
  avatar_url: ''
})

const coupleInfo = ref({
  couple_id: null,
  couple_name: '',
  total_days: 0,
  anniversary_date: '',
  partner_info: {
    nickname: '',
    avatar_url: ''
  }
})

const userProgress = ref({
  today_total: 0,
  completion_rate: 0
})

const partnerProgress = ref({
  today_total: 0,
  completion_rate: 0
})

const sharedAchievements = ref([])
const inviteCode = ref('')
const generatedInviteCode = ref('')

// 弹窗引用
const joinPopup = ref()
const inviteSuccessPopup = ref()

// 页面加载时获取数据
onMounted(() => {
  loadUserInfo()
  loadCoupleInfo()
  loadTodayProgress()
  loadSharedAchievements()
})

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await request.get('/user')
    userInfo.value = response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取情侣信息
const loadCoupleInfo = async () => {
  try {
    const response = await request.get('/couple')
    coupleInfo.value = response.data
  } catch (error) {
    console.error('获取情侣信息失败:', error)
  }
}

// 获取今日进度
const loadTodayProgress = async () => {
  try {
    const response = await request.get('/water/todayProgress')
    userProgress.value = response.data
    
    // 获取伴侣进度（这里需要后端支持）
    // const partnerResponse = await request.get('/couple/partnerProgress')
    // partnerProgress.value = partnerResponse.data
  } catch (error) {
    console.error('获取今日进度失败:', error)
  }
}

// 获取共同成就
const loadSharedAchievements = async () => {
  try {
    const response = await request.get('/achievement', { type: 'couple' })
    sharedAchievements.value = response.data.achievements.slice(0, 3) // 只显示前3个
  } catch (error) {
    console.error('获取共同成就失败:', error)
  }
}

// 创建邀请
const createInvite = async () => {
  try {
    const response = await request.post('/couple')
    generatedInviteCode.value = response.data.invite_code
    inviteSuccessPopup.value.open()
  } catch (error) {
    console.error('创建邀请失败:', error)
    uni.showToast({
      title: '创建邀请失败',
      icon: 'error'
    })
  }
}

// 显示加入弹窗
const showJoinModal = () => {
  joinPopup.value.open()
}

// 关闭加入弹窗
const closeJoinModal = () => {
  joinPopup.value.close()
  inviteCode.value = ''
}

// 加入情侣
const joinCouple = async () => {
  if (!inviteCode.value) {
    uni.showToast({
      title: '请输入邀请码',
      icon: 'none'
    })
    return
  }
  
  try {
    await request.post('/couple/accept', {
      invite_code: inviteCode.value
    })
    
    closeJoinModal()
    loadCoupleInfo()
    
    uni.showToast({
      title: '绑定成功！',
      icon: 'success'
    })
  } catch (error) {
    console.error('加入失败:', error)
    uni.showToast({
      title: '邀请码无效或已过期',
      icon: 'error'
    })
  }
}

// 复制邀请码
const copyInviteCode = () => {
  uni.setClipboardData({
    data: generatedInviteCode.value,
    success: () => {
      uni.showToast({
        title: '邀请码已复制',
        icon: 'success'
      })
    }
  })
}

// 分享邀请码
const shareInviteCode = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '',
    title: '嘉依打卡邀请',
    summary: `我邀请你一起使用嘉依打卡！邀请码：${generatedInviteCode.value}`,
    imageUrl: '/static/share-logo.png'
  })
}

// 发送提醒
const sendReminder = async () => {
  try {
    await request.post('/couple/remind', {
      message: '该喝水啦，宝贝！',
      type: 'water_reminder'
    })
    
    uni.showToast({
      title: '提醒发送成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('发送提醒失败:', error)
  }
}

// 发送鼓励
const sendEncouragement = () => {
  uni.showActionSheet({
    itemList: ['加油！你是最棒的！', '继续保持，我相信你！', '一起努力，一起进步！'],
    success: async (res) => {
      const messages = ['加油！你是最棒的！', '继续保持，我相信你！', '一起努力，一起进步！']
      try {
        await request.post('/couple/remind', {
          message: messages[res.tapIndex],
          type: 'encouragement'
        })
        
        uni.showToast({
          title: '鼓励发送成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('发送鼓励失败:', error)
      }
    }
  })
}

// 创建挑战
const createChallenge = () => {
  uni.navigateTo({ url: '/pages/challenge/create' })
}

// 送礼物
const sendGift = () => {
  uni.navigateTo({ url: '/pages/gift/gift' })
}

// 查看所有成就
const viewAllAchievements = () => {
  uni.navigateTo({ url: '/pages/achievement/couple' })
}

// 情侣设置
const showCoupleSettings = () => {
  uni.navigateTo({ url: '/pages/couple/settings' })
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.couple-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
  padding: 0 32rpx 32rpx;
}

/* 未绑定状态样式 */
.no-couple-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  text-align: center;
}

.empty-illustration {
  margin-bottom: 64rpx;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.bind-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  max-width: 400rpx;
}

.create-invite-btn, .join-invite-btn {
  height: 88rpx;
  border: none;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.create-invite-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

.join-invite-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #FF69B4;
  border: 2rpx solid #FF69B4;
}

.create-invite-btn:active, .join-invite-btn:active {
  transform: translateY(2rpx);
}

/* 已绑定状态样式 */
.couple-content {
  padding-top: 32rpx;
}

/* 情侣信息卡片样式 */
.couple-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(255, 182, 193, 0.2);
  text-align: center;
}

.couple-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.user-avatar-section, .partner-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.user-avatar, .partner-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #FFB6C1;
  margin-bottom: 12rpx;
}

.user-name, .partner-name {
  font-size: 24rpx;
  color: #666;
}

.love-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 24rpx;
}

.heart-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.together-days {
  font-size: 20rpx;
  color: #FF69B4;
  font-weight: bold;
}

.couple-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 16rpx;
}

.anniversary {
  font-size: 24rpx;
  color: #999;
}

/* 进度对比样式 */
.progress-comparison {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-btn {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: normal;
}

.progress-bars {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-progress, .partner-progress {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.mini-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
}

.progress-name {
  flex: 1;
  font-size: 24rpx;
  color: #333;
}

.progress-amount {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

.progress-bar {
  height: 12rpx;
  background: #FFE4E1;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 互动功能样式 */
.interaction-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.interaction-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.interaction-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
}

.interaction-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.interaction-btn text {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

/* 共同成就样式 */
.shared-achievements {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.achievement-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #FFF0F5;
  border-radius: 16rpx;
}

.achievement-icon {
  width: 48rpx;
  height: 48rpx;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.achievement-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.achievement-date {
  font-size: 20rpx;
  color: #999;
}

/* 设置按钮样式 */
.couple-settings {
  text-align: center;
}

.settings-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #FF69B4;
  border: 2rpx solid #FF69B4;
  border-radius: 48rpx;
  padding: 16rpx 48rpx;
  font-size: 24rpx;
}

/* 弹窗样式 */
.join-popup, .invite-success-popup {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  width: 600rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.popup-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-content {
  margin-bottom: 32rpx;
}

.input-group {
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 0 24rpx;
  border: 2rpx solid #FFE4E1;
}

.invite-code-input {
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  text-align: center;
}

.invite-code-display {
  text-align: center;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.invite-code {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  letter-spacing: 8rpx;
}

.invite-tips {
  display: block;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

.popup-actions {
  display: flex;
  gap: 24rpx;
}

.cancel-btn, .confirm-btn, .copy-btn, .share-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.cancel-btn {
  background: #F5F5F5;
  color: #666;
}

.confirm-btn, .copy-btn, .share-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}
</style>
