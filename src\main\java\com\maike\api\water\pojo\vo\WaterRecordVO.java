package com.maike.api.water.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 喝水记录响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class WaterRecordVO {

    /**
     * 记录ID
     */
    private String id;

    /**
     * 喝水量，单位毫升(ml)
     */
    private Integer amountMl;

    /**
     * 记录时间，用户打卡的具体时间
     */
    private LocalDateTime recordedAt;

    /**
     * 杯子类型：default-默认，small-小杯，medium-中杯，large-大杯
     */
    private String cupType;

    /**
     * 杯子类型显示文本
     */
    private String cupTypeText;

    /**
     * 杯子容量，单位毫升
     */
    private Integer cupSizeMl;

    /**
     * 喝水备注，用户可添加心情或说明
     */
    private String note;

    /**
     * 打卡地点，可选记录
     */
    private String location;

    /**
     * 水温：hot-热水，warm-温水，cold-凉水，ice-冰水
     */
    private String temperature;

    /**
     * 水温显示文本
     */
    private String temperatureText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
