package com.maike.api.couple.controller;

import com.maike.api.common.result.Result;
import com.maike.api.common.utils.JwtUtils;
import com.maike.api.couple.pojo.dto.CoupleAcceptDTO;
import com.maike.api.couple.pojo.dto.CoupleCreateDTO;
import com.maike.api.couple.pojo.dto.CoupleUpdateDTO;
import com.maike.api.couple.pojo.vo.CoupleCreateVO;
import com.maike.api.couple.pojo.vo.CoupleInfoVO;
import com.maike.api.couple.service.CoupleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 情侣关系控制器
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/couple")
@RequiredArgsConstructor
@Validated
public class CoupleController {

    private final CoupleService coupleService;
    private final JwtUtils jwtUtils;

    /**
     * 创建情侣邀请
     * 
     * @param createDTO 创建信息
     * @param request   HTTP请求
     * @return 创建结果
     */
    @PostMapping
    public Result<CoupleCreateVO> createInvite(@Valid @RequestBody CoupleCreateDTO createDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        CoupleCreateVO createVO = coupleService.createInvite(userId, createDTO);
        return Result.success("邀请码创建成功", createVO);
    }

    /**
     * 接受情侣邀请
     * 
     * @param acceptDTO 接受邀请信息
     * @param request   HTTP请求
     * @return 接受结果
     */
    @PostMapping("/accept")
    public Result<Void> acceptInvite(@Valid @RequestBody CoupleAcceptDTO acceptDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        coupleService.acceptInvite(userId, acceptDTO);
        return Result.success("绑定成功");
    }

    /**
     * 获取情侣信息
     * 
     * @param request HTTP请求
     * @return 情侣信息
     */
    @GetMapping
    public Result<CoupleInfoVO> getCoupleInfo(HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        CoupleInfoVO coupleInfo = coupleService.getCoupleInfo(userId);
        return Result.success("获取成功", coupleInfo);
    }

    /**
     * 更新情侣信息
     * 
     * @param updateDTO 更新信息
     * @param request   HTTP请求
     * @return 更新结果
     */
    @PutMapping
    public Result<Map<String, Object>> updateCoupleInfo(@Valid @RequestBody CoupleUpdateDTO updateDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        List<String> updatedFields = coupleService.updateCoupleInfo(userId, updateDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("updated_fields", updatedFields);
        
        return Result.success("更新成功", result);
    }

    /**
     * 解除情侣关系
     * 
     * @param request HTTP请求
     * @return 解除结果
     */
    @DeleteMapping
    public Result<Void> unbindCouple(HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        coupleService.unbindCouple(userId);
        return Result.success("解除成功");
    }

    /**
     * 从请求中获取当前用户ID
     * 
     * @param request HTTP请求
     * @return 用户ID
     */
    private String getCurrentUserId(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        return jwtUtils.getUserIdFromToken(token);
    }

    /**
     * 从请求中获取JWT令牌
     * 
     * @param request HTTP请求
     * @return JWT令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}
