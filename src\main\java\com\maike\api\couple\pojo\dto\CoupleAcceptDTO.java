package com.maike.api.couple.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 接受情侣邀请请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleAcceptDTO {

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空")
    @Pattern(regexp = "^[A-Z0-9]{6,10}$", message = "邀请码格式不正确")
    private String inviteCode;
}
