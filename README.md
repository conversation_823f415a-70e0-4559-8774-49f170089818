# 嘉依打卡：元气水杯与Ta的约定

一款专为情侣设计的喝水打卡应用，让爱情在每一滴水中流淌。

## 🌟 项目特色

- 💕 **情侣互动**: 专为情侣设计的喝水打卡系统
- 📱 **多端支持**: 支持Web、移动端等多平台
- 🎯 **目标管理**: 个性化喝水目标设置和追踪
- 🏆 **成就系统**: 丰富的成就体系，增加趣味性
- 📹 **视频分享**: 记录美好时光，分享生活点滴
- 🎮 **挑战模式**: 情侣间的有趣挑战，增进感情
- 🔔 **智能提醒**: 个性化提醒设置，贴心关怀

## 🛠️ 技术栈

### 后端技术
- **框架**: Spring Boot 3.2.1
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **ORM**: MyBatis-Plus 3.5.5
- **安全**: Spring Security + JWT
- **文档**: Swagger/OpenAPI 3
- **构建**: Maven 3.9+

### 开发工具
- **JDK**: OpenJDK 17
- **IDE**: IntelliJ IDEA / Eclipse
- **版本控制**: Git
- **容器化**: Docker + Docker Compose

## 📋 功能模块

### 1. 用户认证模块
- 用户注册/登录
- 手机验证码
- 密码管理
- 用户信息管理

### 2. 情侣绑定模块
- 创建情侣邀请
- 接受邀请绑定
- 情侣信息管理
- 解除绑定关系

### 3. 喝水打卡模块
- 喝水记录管理
- 目标设置追踪
- 统计数据分析
- 历史记录查询

### 4. 视频功能模块
- 视频上传分享
- 点赞评论互动
- 视频列表浏览
- 隐私权限控制

### 5. 成就系统模块
- 成就列表展示
- 自动解锁机制
- 进度追踪显示
- 奖励系统管理

### 6. 挑战功能模块
- 创建情侣挑战
- 挑战进度管理
- 完成状态追踪
- 奖励发放机制

### 7. 通知提醒模块
- 系统通知管理
- 个性化提醒设置
- 消息推送服务
- 通知状态管理

## 🚀 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/your-org/water-backend.git
cd water-backend
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE water_tracker CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p water_tracker < docs/database.sql
```

3. **配置应用**
```bash
# 复制配置文件
cp src/main/resources/application-dev.yml.example src/main/resources/application-dev.yml

# 编辑配置文件，修改数据库连接等信息
vim src/main/resources/application-dev.yml
```

4. **启动应用**
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者
java -jar target/water-backend-*.jar
```

5. **访问应用**
- 应用地址: http://localhost:8888/api/v1
- API文档: http://localhost:8888/api/v1/swagger-ui.html
- 健康检查: http://localhost:8888/api/v1/actuator/health

### Docker部署

1. **准备环境**
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env
```

2. **一键部署**
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

3. **手动部署**
```bash
# 构建应用
mvn clean package -DskipTests

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps
```

## 📖 API文档

### 接口规范
- 基础路径: `/api/v1`
- 认证方式: Bearer Token (JWT)
- 数据格式: JSON
- 字符编码: UTF-8

### 主要接口

#### 用户认证
- `POST /user/register` - 用户注册
- `POST /user/login` - 用户登录
- `GET /user/info` - 获取用户信息
- `PUT /user/info` - 更新用户信息

#### 情侣绑定
- `POST /couple` - 创建情侣邀请
- `POST /couple/accept` - 接受邀请
- `GET /couple` - 获取情侣信息
- `DELETE /couple` - 解除关系

#### 喝水打卡
- `POST /water` - 添加喝水记录
- `GET /water` - 获取记录列表
- `POST /water/goal` - 设置目标
- `GET /water/stats` - 获取统计

更多接口详情请查看 [API文档](docs/api-documentation.md)

## 🗄️ 数据库设计

数据库采用MySQL 8.0，包含以下主要表：

- `users` - 用户信息表
- `couples` - 情侣关系表
- `water_records` - 喝水记录表
- `water_goals` - 喝水目标表
- `videos` - 视频信息表
- `achievements` - 成就定义表
- `user_achievements` - 用户成就表
- `challenges` - 挑战信息表
- `notifications` - 通知消息表
- `reminders` - 提醒设置表

详细的数据库设计请查看 [数据库文档](docs/database.sql)

## 🔧 配置说明

### 应用配置
主要配置文件位于 `src/main/resources/` 目录：

- `application.yml` - 主配置文件
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置

### 环境变量
支持通过环境变量覆盖配置：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=water_tracker
DB_USERNAME=root
DB_PASSWORD=123456

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your_secret_key
JWT_EXPIRATION=86400
```

## 🧪 测试

### 单元测试
```bash
# 运行所有测试
mvn test

# 运行指定测试类
mvn test -Dtest=UserServiceTest

# 生成测试报告
mvn surefire-report:report
```

### 集成测试
```bash
# 运行集成测试
mvn verify

# 使用测试配置文件
mvn test -Dspring.profiles.active=test
```

## 📊 监控

应用集成了Spring Boot Actuator，提供以下监控端点：

- `/actuator/health` - 健康检查
- `/actuator/info` - 应用信息
- `/actuator/metrics` - 性能指标
- `/actuator/prometheus` - Prometheus指标

## 🚀 部署

### 生产环境部署

1. **服务器要求**
   - CPU: 2核心以上
   - 内存: 4GB以上
   - 存储: 50GB以上
   - 操作系统: Linux (推荐Ubuntu 20.04+)

2. **部署步骤**
   ```bash
   # 1. 上传代码到服务器
   git clone https://github.com/your-org/water-backend.git
   cd water-backend
   
   # 2. 配置环境变量
   cp .env.example .env
   vim .env
   
   # 3. 执行部署脚本
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **反向代理配置**
   推荐使用Nginx作为反向代理，配置文件示例：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location /api/v1/ {
           proxy_pass http://localhost:8888;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 团队

- **Maike Team** - 项目维护者
- **联系邮箱**: <EMAIL>
- **官方网站**: https://watertracker.com

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**让爱情在每一滴水中流淌 💕**
