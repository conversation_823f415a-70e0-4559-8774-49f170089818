package com.maike.api.couple.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 解除情侣关系请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleUnbindDTO {

    /**
     * 解绑原因
     */
    @Size(max = 200, message = "解绑原因长度不能超过200字符")
    private String reason;

    /**
     * 密码确认
     */
    @NotBlank(message = "密码不能为空")
    private String password;
}
