package com.maike.api.achievement.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 成就实体类
 * 对应数据库表：achievements
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("achievements")
public class Achievement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成就ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 成就名称，如"初次打卡"、"连续7天"
     */
    @TableField("name")
    private String name;

    /**
     * 成就描述，详细说明获得条件
     */
    @TableField("description")
    private String description;

    /**
     * 成就图标URL地址
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 成就类型：personal-个人成就，couple-情侣成就，system-系统成就
     */
    @TableField("type")
    private String type;

    /**
     * 成就分类：water-喝水相关，video-视频相关，social-社交相关
     */
    @TableField("category")
    private String category;

    /**
     * 获得条件，JSON格式存储复杂条件
     */
    @TableField("requirements")
    private String requirements;

    /**
     * 成就积分，用于排行榜等功能
     */
    @TableField("points")
    private Integer points;

    /**
     * 稀有度：common-普通，rare-稀有，epic-史诗，legendary-传说
     */
    @TableField("rarity")
    private String rarity;

    /**
     * 是否隐藏成就：1-隐藏，0-显示
     */
    @TableField("is_hidden")
    private Boolean isHidden;

    /**
     * 解锁顺序，用于成就链
     */
    @TableField("unlock_order")
    private Integer unlockOrder;

    /**
     * 前置成就ID，需要先完成的成就
     */
    @TableField("prerequisite_id")
    private String prerequisiteId;

    /**
     * 奖励类型：theme-主题，sticker-贴纸，title-称号
     */
    @TableField("reward_type")
    private String rewardType;

    /**
     * 奖励内容，具体的奖励物品
     */
    @TableField("reward_value")
    private String rewardValue;

    /**
     * 成就状态：0-正常，1-已下线，2-已删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "2")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断成就是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断成就是否已下线
     * 
     * @return true-已下线，false-未下线
     */
    public boolean isOffline() {
        return "1".equals(this.status);
    }

    /**
     * 判断成就是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "2".equals(this.status);
    }

    /**
     * 获取成就类型显示文本
     * 
     * @return 成就类型显示文本
     */
    public String getTypeText() {
        if (type == null) {
            return "个人成就";
        }
        
        switch (type) {
            case "personal":
                return "个人成就";
            case "couple":
                return "情侣成就";
            case "system":
                return "系统成就";
            default:
                return "个人成就";
        }
    }

    /**
     * 获取成就分类显示文本
     * 
     * @return 成就分类显示文本
     */
    public String getCategoryText() {
        if (category == null) {
            return "喝水相关";
        }
        
        switch (category) {
            case "water":
                return "喝水相关";
            case "video":
                return "视频相关";
            case "social":
                return "社交相关";
            default:
                return "喝水相关";
        }
    }

    /**
     * 获取稀有度显示文本
     * 
     * @return 稀有度显示文本
     */
    public String getRarityText() {
        if (rarity == null) {
            return "普通";
        }
        
        switch (rarity) {
            case "common":
                return "普通";
            case "rare":
                return "稀有";
            case "epic":
                return "史诗";
            case "legendary":
                return "传说";
            default:
                return "普通";
        }
    }

    /**
     * 获取稀有度颜色
     * 
     * @return 稀有度颜色
     */
    public String getRarityColor() {
        if (rarity == null) {
            return "#999999";
        }
        
        switch (rarity) {
            case "common":
                return "#999999"; // 灰色
            case "rare":
                return "#1890ff"; // 蓝色
            case "epic":
                return "#722ed1"; // 紫色
            case "legendary":
                return "#fa8c16"; // 橙色
            default:
                return "#999999";
        }
    }

    /**
     * 获取奖励类型显示文本
     * 
     * @return 奖励类型显示文本
     */
    public String getRewardTypeText() {
        if (rewardType == null) {
            return "无奖励";
        }
        
        switch (rewardType) {
            case "theme":
                return "主题";
            case "sticker":
                return "贴纸";
            case "title":
                return "称号";
            default:
                return "无奖励";
        }
    }

    /**
     * 判断是否为个人成就
     * 
     * @return true-个人成就，false-非个人成就
     */
    public boolean isPersonal() {
        return "personal".equals(this.type);
    }

    /**
     * 判断是否为情侣成就
     * 
     * @return true-情侣成就，false-非情侣成就
     */
    public boolean isCouple() {
        return "couple".equals(this.type);
    }

    /**
     * 判断是否为系统成就
     * 
     * @return true-系统成就，false-非系统成就
     */
    public boolean isSystem() {
        return "system".equals(this.type);
    }
}
