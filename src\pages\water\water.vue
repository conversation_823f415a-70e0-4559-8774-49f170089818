<template>
  <view class="page">
    <!-- 顶部进度显示 -->
    <view class="progress-header">
      <view class="date-info">
        <text class="date">{{ currentDate }}</text>
        <text class="day">{{ currentDay }}</text>
      </view>
      
      <view class="progress-circle">
        <view class="circle-bg">
          <view class="circle-fill" :style="{ transform: `rotate(${progressAngle}deg)` }"></view>
          <view class="circle-inner">
            <text class="progress-text">{{ completionRate }}%</text>
            <text class="progress-label">完成度</text>
          </view>
        </view>
      </view>
      
      <view class="goal-info">
        <text class="current">{{ currentAmount }}</text>
        <text class="separator">/</text>
        <text class="target">{{ dailyGoal }}ml</text>
      </view>
    </view>

    <!-- 快捷打卡区域 -->
    <view class="quick-drink-section">
      <view class="section-title">
        <text>快捷打卡</text>
      </view>
      
      <view class="cup-grid">
        <view class="cup-item" v-for="cup in cupTypes" :key="cup.type" @click="quickDrink(cup)">
          <image class="cup-image" :src="cup.image" mode="aspectFit"></image>
          <text class="cup-name">{{ cup.name }}</text>
          <text class="cup-volume">{{ cup.volume }}ml</text>
        </view>
      </view>
    </view>

    <!-- 自定义打卡 -->
    <view class="custom-drink-section">
      <view class="section-title">
        <text>自定义打卡</text>
      </view>
      
      <view class="custom-form">
        <view class="form-item">
          <text class="label">喝水量</text>
          <view class="input-group">
            <input 
              class="amount-input" 
              type="number" 
              v-model="customAmount" 
              placeholder="请输入毫升数"
            />
            <text class="unit">ml</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">水温</text>
          <view class="temperature-options">
            <view 
              class="temp-option" 
              v-for="temp in temperatureOptions" 
              :key="temp.value"
              :class="{ active: selectedTemperature === temp.value }"
              @click="selectedTemperature = temp.value"
            >
              <text>{{ temp.label }}</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">备注</text>
          <textarea 
            class="note-input" 
            v-model="drinkNote" 
            placeholder="记录一下心情或感受吧~"
            maxlength="100"
          ></textarea>
        </view>
        
        <button class="submit-btn" @click="customDrink" :disabled="!customAmount">
          确认打卡
        </button>
      </view>
    </view>

    <!-- 今日记录 -->
    <view class="records-section">
      <view class="section-title">
        <text>今日记录</text>
        <text class="record-count">共{{ todayRecords.length }}次</text>
      </view>
      
      <view class="records-list">
        <view class="record-item" v-for="record in todayRecords" :key="record.id">
          <view class="record-time">
            <text>{{ formatTime(record.recorded_at) }}</text>
          </view>
          <view class="record-content">
            <view class="record-amount">
              <text class="amount">{{ record.amount_ml }}ml</text>
              <text class="temperature" v-if="record.temperature">{{ getTemperatureLabel(record.temperature) }}</text>
            </view>
            <text class="record-note" v-if="record.note">{{ record.note }}</text>
          </view>
          <view class="record-actions">
            <text class="edit-btn" @click="editRecord(record)">编辑</text>
            <text class="delete-btn" @click="deleteRecord(record.id)">删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 目标设置弹窗 -->
    <uni-popup ref="goalPopup" type="center">
      <view class="goal-popup">
        <view class="popup-header">
          <text class="popup-title">设置每日目标</text>
        </view>
        <view class="popup-content">
          <view class="goal-input-group">
            <input 
              class="goal-input" 
              type="number" 
              v-model="newGoal" 
              placeholder="请输入目标水量"
            />
            <text class="goal-unit">ml</text>
          </view>
          <view class="goal-tips">
            <text>建议每日饮水量：成人2000-2500ml</text>
          </view>
        </view>
        <view class="popup-actions">
          <button class="cancel-btn" @click="closeGoalPopup">取消</button>
          <button class="confirm-btn" @click="updateGoal">确认</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import request from '@/utils/request'

// 响应式数据
const currentAmount = ref(0)
const dailyGoal = ref(2000)
const customAmount = ref('')
const selectedTemperature = ref('warm')
const drinkNote = ref('')
const todayRecords = ref([])
const newGoal = ref(2000)

// 杯子类型配置
const cupTypes = ref([
  { type: 'small', name: '小杯', volume: 200, image: '/static/cup-small.png' },
  { type: 'medium', name: '中杯', volume: 300, image: '/static/cup-medium.png' },
  { type: 'large', name: '大杯', volume: 500, image: '/static/cup-large.png' },
  { type: 'bottle', name: '水瓶', volume: 750, image: '/static/cup-bottle.png' }
])

// 水温选项
const temperatureOptions = ref([
  { value: 'ice', label: '冰水' },
  { value: 'cold', label: '凉水' },
  { value: 'warm', label: '温水' },
  { value: 'hot', label: '热水' }
])

// 计算属性
const currentDate = computed(() => {
  const date = new Date()
  return `${date.getMonth() + 1}月${date.getDate()}日`
})

const currentDay = computed(() => {
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return days[new Date().getDay()]
})

const completionRate = computed(() => {
  return Math.min(Math.round((currentAmount.value / dailyGoal.value) * 100), 100)
})

const progressAngle = computed(() => {
  return (currentAmount.value / dailyGoal.value) * 360
})

// 页面加载时获取数据
onMounted(() => {
  loadTodayProgress()
  loadTodayRecords()
})

// 获取今日进度
const loadTodayProgress = async () => {
  try {
    const response = await request.get('/water/todayProgress')
    currentAmount.value = response.data.today_total
    dailyGoal.value = response.data.daily_goal
  } catch (error) {
    console.error('获取今日进度失败:', error)
  }
}

// 获取今日记录
const loadTodayRecords = async () => {
  try {
    const today = new Date().toISOString().split('T')[0]
    const response = await request.get('/water', { date: today })
    todayRecords.value = response.data.records
  } catch (error) {
    console.error('获取今日记录失败:', error)
  }
}

// 快捷打卡
const quickDrink = async (cup: any) => {
  try {
    const response = await request.post('/water', {
      amount_ml: cup.volume,
      cup_type: cup.type,
      temperature: selectedTemperature.value,
      recorded_at: new Date().toISOString()
    })
    
    // 更新当前进度
    currentAmount.value = response.data.today_total
    
    // 刷新记录列表
    loadTodayRecords()
    
    uni.showToast({
      title: `打卡成功！+${cup.volume}ml`,
      icon: 'success'
    })
  } catch (error) {
    console.error('打卡失败:', error)
    uni.showToast({
      title: '打卡失败，请重试',
      icon: 'error'
    })
  }
}

// 自定义打卡
const customDrink = async () => {
  if (!customAmount.value || customAmount.value <= 0) {
    uni.showToast({
      title: '请输入有效的水量',
      icon: 'none'
    })
    return
  }
  
  try {
    const response = await request.post('/water', {
      amount_ml: parseInt(customAmount.value),
      temperature: selectedTemperature.value,
      note: drinkNote.value,
      recorded_at: new Date().toISOString()
    })
    
    // 更新当前进度
    currentAmount.value = response.data.today_total
    
    // 清空表单
    customAmount.value = ''
    drinkNote.value = ''
    
    // 刷新记录列表
    loadTodayRecords()
    
    uni.showToast({
      title: `打卡成功！+${customAmount.value}ml`,
      icon: 'success'
    })
  } catch (error) {
    console.error('打卡失败:', error)
    uni.showToast({
      title: '打卡失败，请重试',
      icon: 'error'
    })
  }
}

// 删除记录
const deleteRecord = async (recordId: string) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await request.delete(`/water/${recordId}`)
          
          // 刷新数据
          loadTodayProgress()
          loadTodayRecords()
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'error'
          })
        }
      }
    }
  })
}

// 编辑记录
const editRecord = (record: any) => {
  // 跳转到编辑页面或显示编辑弹窗
  uni.navigateTo({
    url: `/pages/water/edit?id=${record.id}`
  })
}

// 格式化时间
const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 获取水温标签
const getTemperatureLabel = (temp: string) => {
  const option = temperatureOptions.value.find(t => t.value === temp)
  return option ? option.label : ''
}

// 目标设置相关方法
const goalPopup = ref()

const openGoalPopup = () => {
  newGoal.value = dailyGoal.value
  goalPopup.value.open()
}

const closeGoalPopup = () => {
  goalPopup.value.close()
}

const updateGoal = async () => {
  try {
    await request.post('/water/goal', {
      daily_goal_ml: newGoal.value,
      goal_date: new Date().toISOString().split('T')[0]
    })
    
    dailyGoal.value = newGoal.value
    closeGoalPopup()
    
    uni.showToast({
      title: '目标设置成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('设置目标失败:', error)
    uni.showToast({
      title: '设置失败，请重试',
      icon: 'error'
    })
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
  padding: 0 32rpx 32rpx;
}

/* 顶部进度显示样式 */
.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 12rpx 48rpx rgba(255, 182, 193, 0.2);
}

.date-info {
  text-align: center;
}

.date {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.day {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.progress-circle {
  position: relative;
}

.circle-bg {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #FFE4E1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-fill {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(#FF69B4 0deg, #FFE4E1 0deg);
  transition: transform 0.5s ease;
}

.circle-inner {
  position: relative;
  z-index: 2;
  background: white;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.2);
}

.progress-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF69B4;
  line-height: 1;
}

.progress-label {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

.goal-info {
  text-align: center;
}

.current {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
}

.separator {
  font-size: 28rpx;
  color: #999;
  margin: 0 8rpx;
}

.target {
  font-size: 28rpx;
  color: #666;
}

/* 区域标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* 快捷打卡区域样式 */
.quick-drink-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.cup-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.cup-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
}

.cup-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 182, 193, 0.25);
}

.cup-image {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
}

.cup-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.cup-volume {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

/* 自定义打卡区域样式 */
.custom-drink-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.custom-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.input-group {
  display: flex;
  align-items: center;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 0 24rpx;
  border: 2rpx solid #FFE4E1;
}

.amount-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
}

.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.temperature-options {
  display: flex;
  gap: 16rpx;
}

.temp-option {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  background: #FFF0F5;
  border-radius: 12rpx;
  border: 2rpx solid #FFE4E1;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.temp-option.active {
  background: #FF69B4;
  color: white;
  border-color: #FF69B4;
}

.note-input {
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #FFE4E1;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
}

.submit-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:disabled {
  background: #E0E0E0;
  color: #999;
  box-shadow: none;
}

.submit-btn:active:not(:disabled) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);
}

/* 记录列表样式 */
.records-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #FFF0F5;
  border-radius: 16rpx;
  border: 1rpx solid #FFE4E1;
}

.record-time {
  min-width: 80rpx;
  text-align: center;
}

.record-time text {
  font-size: 24rpx;
  color: #999;
}

.record-content {
  flex: 1;
}

.record-amount {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF69B4;
}

.temperature {
  font-size: 20rpx;
  color: #87CEEB;
  background: rgba(135, 206, 235, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.record-note {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.record-actions {
  display: flex;
  gap: 16rpx;
}

.edit-btn, .delete-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.edit-btn {
  color: #87CEEB;
  background: rgba(135, 206, 235, 0.1);
}

.delete-btn {
  color: #FFA07A;
  background: rgba(255, 160, 122, 0.1);
}

/* 弹窗样式 */
.goal-popup {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  width: 600rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.popup-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-content {
  margin-bottom: 32rpx;
}

.goal-input-group {
  display: flex;
  align-items: center;
  background: #FFF0F5;
  border-radius: 16rpx;
  padding: 0 24rpx;
  border: 2rpx solid #FFE4E1;
  margin-bottom: 16rpx;
}

.goal-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  text-align: center;
}

.goal-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.goal-tips {
  text-align: center;
}

.goal-tips text {
  font-size: 24rpx;
  color: #999;
}

.popup-actions {
  display: flex;
  gap: 24rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.cancel-btn {
  background: #F5F5F5;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}
</style>
