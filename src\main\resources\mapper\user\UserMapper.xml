<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maike.api.user.mapper.UserMapper">

    <!-- 用户基础字段 -->
    <sql id="Base_Column_List">
        id, phone_number, nickname, avatar_url, gender, birthday, bio, couple_id,
        password, salt, status, login_ip, login_date, create_by, create_time,
        update_by, update_time, remark
    </sql>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhoneNumber" resultType="com.maike.api.user.pojo.entity.User">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE phone_number = #{phoneNumber}
        AND status != '2'
        LIMIT 1
    </select>

    <!-- 根据手机号查询用户（包含已删除的用户） -->
    <select id="selectByPhoneNumberWithDeleted" resultType="com.maike.api.user.pojo.entity.User">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE phone_number = #{phoneNumber}
        LIMIT 1
    </select>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLoginInfo">
        UPDATE users
        SET login_ip = #{loginIp},
            login_date = NOW(),
            update_time = NOW()
        WHERE id = #{userId}
        AND status = '0'
    </update>

    <!-- 根据昵称查询用户数量 -->
    <select id="countByNickname" resultType="int">
        SELECT COUNT(1)
        FROM users
        WHERE nickname = #{nickname}
        AND status != '2'
        <if test="excludeUserId != null and excludeUserId != ''">
            AND id != #{excludeUserId}
        </if>
    </select>

    <!-- 软删除用户 -->
    <update id="softDeleteUser">
        UPDATE users
        SET status = '2',
            update_time = NOW()
        WHERE id = #{userId}
        AND status != '2'
    </update>

    <!-- 恢复已删除的用户 -->
    <update id="restoreUser">
        UPDATE users
        SET status = '0',
            update_time = NOW()
        WHERE id = #{userId}
        AND status = '2'
    </update>

</mapper>
