package com.maike.api.water.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.water.pojo.dto.WaterGoalCreateDTO;
import com.maike.api.water.pojo.entity.WaterGoal;
import com.maike.api.water.pojo.vo.WaterGoalVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 喝水目标服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface WaterGoalService extends IService<WaterGoal> {

    /**
     * 设置每日喝水目标
     * 
     * @param userId    用户ID
     * @param createDTO 目标信息
     * @return 目标信息
     */
    WaterGoalVO setDailyGoal(String userId, WaterGoalCreateDTO createDTO);

    /**
     * 获取用户指定日期的目标
     * 
     * @param userId   用户ID
     * @param goalDate 目标日期（可选，默认今天）
     * @return 目标信息
     */
    WaterGoalVO getUserGoal(String userId, LocalDate goalDate);

    /**
     * 更新目标的实际完成量
     * 
     * @param userId       用户ID
     * @param goalDate     目标日期
     * @param actualAmount 实际完成量
     */
    void updateActualAmount(String userId, LocalDate goalDate, Integer actualAmount);

    /**
     * 获取或创建用户当日目标
     * 
     * @param userId   用户ID
     * @param goalDate 目标日期
     * @return 目标信息
     */
    WaterGoal getOrCreateUserGoal(String userId, LocalDate goalDate);

    /**
     * 查询用户指定日期范围内的目标
     * 
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 目标列表
     */
    List<WaterGoalVO> getUserGoalsByDateRange(String userId, LocalDate startDate, LocalDate endDate);

    /**
     * 查询用户最近的目标
     * 
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 目标列表
     */
    List<WaterGoalVO> getUserRecentGoals(String userId, Integer limit);

    /**
     * 获取用户目标达成统计
     * 
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 达成统计
     */
    GoalAchievementStatVO getUserAchievementStats(String userId, LocalDate startDate, LocalDate endDate);

    /**
     * 检查并更新目标达成状态
     * 
     * @param userId   用户ID
     * @param goalDate 目标日期
     */
    void checkAndUpdateAchievement(String userId, LocalDate goalDate);

    /**
     * 目标达成统计VO
     */
    class GoalAchievementStatVO {
        private Integer totalGoals;
        private Integer achievedGoals;
        private Double achievementRate;

        // getters and setters
        public Integer getTotalGoals() { return totalGoals; }
        public void setTotalGoals(Integer totalGoals) { this.totalGoals = totalGoals; }
        public Integer getAchievedGoals() { return achievedGoals; }
        public void setAchievedGoals(Integer achievedGoals) { this.achievedGoals = achievedGoals; }
        public Double getAchievementRate() { return achievementRate; }
        public void setAchievementRate(Double achievementRate) { this.achievementRate = achievementRate; }
    }
}
