<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maike.api.water.mapper.WaterRecordMapper">

    <!-- 喝水记录基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, amount_ml, recorded_at, cup_type, cup_size_ml, note, location, 
        temperature, status, create_by, create_time, update_by, update_time, remark
    </sql>

    <!-- 分页查询用户喝水记录 -->
    <select id="selectUserRecordsPage" resultType="com.maike.api.water.pojo.entity.WaterRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_records
        WHERE user_id = #{userId}
        AND status = '0'
        <if test="date != null">
            AND DATE(recorded_at) = #{date}
        </if>
        ORDER BY recorded_at DESC
    </select>

    <!-- 查询用户指定日期的喝水记录 -->
    <select id="selectUserRecordsByDate" resultType="com.maike.api.water.pojo.entity.WaterRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_records
        WHERE user_id = #{userId}
        AND DATE(recorded_at) = #{date}
        AND status = '0'
        ORDER BY recorded_at DESC
    </select>

    <!-- 计算用户指定日期的总喝水量 -->
    <select id="sumUserDailyAmount" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(amount_ml), 0)
        FROM water_records
        WHERE user_id = #{userId}
        AND DATE(recorded_at) = #{date}
        AND status = '0'
    </select>

    <!-- 查询用户指定时间范围内的喝水记录 -->
    <select id="selectUserRecordsByTimeRange" resultType="com.maike.api.water.pojo.entity.WaterRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_records
        WHERE user_id = #{userId}
        AND recorded_at BETWEEN #{startTime} AND #{endTime}
        AND status = '0'
        ORDER BY recorded_at DESC
    </select>

    <!-- 查询用户最近的喝水记录 -->
    <select id="selectUserRecentRecords" resultType="com.maike.api.water.pojo.entity.WaterRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_records
        WHERE user_id = #{userId}
        AND status = '0'
        ORDER BY recorded_at DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户指定日期范围内每日的喝水量 -->
    <select id="selectUserDailyStats" resultType="com.maike.api.water.mapper.WaterRecordMapper$DailyWaterStat">
        SELECT 
            DATE(recorded_at) as date,
            SUM(amount_ml) as totalAmount,
            COUNT(*) as recordCount
        FROM water_records
        WHERE user_id = #{userId}
        AND DATE(recorded_at) BETWEEN #{startDate} AND #{endDate}
        AND status = '0'
        GROUP BY DATE(recorded_at)
        ORDER BY date DESC
    </select>

    <!-- 删除用户指定记录 -->
    <update id="deleteUserRecord">
        UPDATE water_records
        SET status = '1',
            update_time = NOW()
        WHERE id = #{recordId}
        AND user_id = #{userId}
        AND status = '0'
    </update>

</mapper>
