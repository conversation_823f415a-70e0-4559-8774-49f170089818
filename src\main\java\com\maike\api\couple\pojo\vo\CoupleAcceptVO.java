package com.maike.api.couple.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 接受情侣邀请响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleAcceptVO {

    /**
     * 情侣关系ID
     */
    private String coupleId;

    /**
     * 伴侣信息
     */
    private PartnerInfo partnerInfo;

    /**
     * 绑定时间
     */
    private LocalDateTime boundAt;

    /**
     * 伴侣信息内部类
     */
    @Data
    public static class PartnerInfo {
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 头像URL
         */
        private String avatarUrl;
    }
}
