package com.maike.api.water.pojo.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 添加喝水记录请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class WaterRecordCreateDTO {

    /**
     * 喝水量，单位毫升(ml)
     */
    @NotNull(message = "喝水量不能为空")
    @Min(value = 1, message = "喝水量不能小于1ml")
    @Max(value = 5000, message = "喝水量不能超过5000ml")
    private Integer amountMl;

    /**
     * 杯子类型：default-默认，small-小杯，medium-中杯，large-大杯，bottle-水瓶
     */
    @Size(max = 50, message = "杯子类型长度不能超过50个字符")
    private String cupType;

    /**
     * 水温：hot-热水，warm-温水，cold-凉水，ice-冰水
     */
    @Size(max = 20, message = "水温类型长度不能超过20个字符")
    private String temperature;

    /**
     * 喝水备注，用户可添加心情或说明
     */
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String note;

    /**
     * 打卡地点，可选记录
     */
    @Size(max = 100, message = "地点长度不能超过100个字符")
    private String location;

    /**
     * 记录时间，用户打卡的具体时间（可选，默认当前时间）
     */
    private LocalDateTime recordedAt;
}
