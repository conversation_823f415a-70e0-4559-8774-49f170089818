package com.maike.api.water.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.maike.api.common.result.Result;
import com.maike.api.common.utils.JwtUtils;
import com.maike.api.water.pojo.dto.WaterGoalCreateDTO;
import com.maike.api.water.pojo.dto.WaterRecordCreateDTO;
import com.maike.api.water.pojo.dto.WaterRecordUpdateDTO;
import com.maike.api.water.pojo.vo.WaterGoalVO;
import com.maike.api.water.pojo.vo.WaterRecordCreateVO;
import com.maike.api.water.pojo.vo.WaterRecordVO;
import com.maike.api.water.service.WaterGoalService;
import com.maike.api.water.service.WaterRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 喝水打卡控制器
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/water")
@RequiredArgsConstructor
@Validated
public class WaterController {

    private final WaterRecordService waterRecordService;
    private final WaterGoalService waterGoalService;
    private final JwtUtils jwtUtils;

    /**
     * 添加喝水记录
     * 
     * @param createDTO 创建信息
     * @param request   HTTP请求
     * @return 创建结果
     */
    @PostMapping
    public Result<WaterRecordCreateVO> addWaterRecord(@Valid @RequestBody WaterRecordCreateDTO createDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        WaterRecordCreateVO createVO = waterRecordService.addWaterRecord(userId, createDTO);
        return Result.success("打卡成功", createVO);
    }

    /**
     * 获取喝水记录列表
     * 
     * @param date    查询日期（可选）
     * @param page    页码，默认1
     * @param size    每页数量，默认20
     * @param request HTTP请求
     * @return 记录列表
     */
    @GetMapping
    public Result<IPage<WaterRecordVO>> getWaterRecords(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date,
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        IPage<WaterRecordVO> recordPage = waterRecordService.getUserRecordsPage(userId, date, page, size);
        return Result.success("获取成功", recordPage);
    }

    /**
     * 更新喝水记录
     * 
     * @param recordId  记录ID
     * @param updateDTO 更新信息
     * @param request   HTTP请求
     * @return 更新结果
     */
    @PutMapping("/{recordId}")
    public Result<Map<String, Object>> updateWaterRecord(@PathVariable String recordId, 
                                                         @Valid @RequestBody WaterRecordUpdateDTO updateDTO, 
                                                         HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        List<String> updatedFields = waterRecordService.updateWaterRecord(userId, recordId, updateDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("updated_fields", updatedFields);
        
        return Result.success("更新成功", result);
    }

    /**
     * 删除喝水记录
     * 
     * @param recordId 记录ID
     * @param request  HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/{recordId}")
    public Result<Void> deleteWaterRecord(@PathVariable String recordId, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        waterRecordService.deleteWaterRecord(userId, recordId);
        return Result.success("删除成功");
    }

    /**
     * 设置每日目标
     * 
     * @param createDTO 目标信息
     * @param request   HTTP请求
     * @return 设置结果
     */
    @PostMapping("/goal")
    public Result<WaterGoalVO> setDailyGoal(@Valid @RequestBody WaterGoalCreateDTO createDTO, HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        WaterGoalVO goalVO = waterGoalService.setDailyGoal(userId, createDTO);
        return Result.success("目标设置成功", goalVO);
    }

    /**
     * 获取每日目标
     * 
     * @param date    查询日期（可选，默认今天）
     * @param request HTTP请求
     * @return 目标信息
     */
    @GetMapping("/goal")
    public Result<WaterGoalVO> getDailyGoal(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date,
                                            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        WaterGoalVO goalVO = waterGoalService.getUserGoal(userId, date);
        return Result.success("获取成功", goalVO);
    }

    /**
     * 获取喝水统计数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param request   HTTP请求
     * @return 统计数据
     */
    @GetMapping("/stats")
    public Result<List<WaterRecordService.DailyWaterStatVO>> getWaterStats(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        List<WaterRecordService.DailyWaterStatVO> stats = waterRecordService.getUserWaterStats(userId, startDate, endDate);
        return Result.success("获取成功", stats);
    }

    /**
     * 获取目标达成统计
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param request   HTTP请求
     * @return 达成统计
     */
    @GetMapping("/goal/stats")
    public Result<WaterGoalService.GoalAchievementStatVO> getGoalStats(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        WaterGoalService.GoalAchievementStatVO stats = waterGoalService.getUserAchievementStats(userId, startDate, endDate);
        return Result.success("获取成功", stats);
    }

    /**
     * 获取最近的喝水记录
     * 
     * @param limit   限制数量，默认10
     * @param request HTTP请求
     * @return 最近记录
     */
    @GetMapping("/recent")
    public Result<List<WaterRecordVO>> getRecentRecords(@RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit,
                                                        HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        List<WaterRecordVO> records = waterRecordService.getUserRecentRecords(userId, limit);
        return Result.success("获取成功", records);
    }

    /**
     * 获取最近的目标
     * 
     * @param limit   限制数量，默认7
     * @param request HTTP请求
     * @return 最近目标
     */
    @GetMapping("/goal/recent")
    public Result<List<WaterGoalVO>> getRecentGoals(@RequestParam(defaultValue = "7") @Min(1) @Max(30) Integer limit,
                                                    HttpServletRequest request) {
        String userId = getCurrentUserId(request);
        List<WaterGoalVO> goals = waterGoalService.getUserRecentGoals(userId, limit);
        return Result.success("获取成功", goals);
    }

    /**
     * 从请求中获取当前用户ID
     * 
     * @param request HTTP请求
     * @return 用户ID
     */
    private String getCurrentUserId(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        return jwtUtils.getUserIdFromToken(token);
    }

    /**
     * 从请求中获取JWT令牌
     * 
     * @param request HTTP请求
     * @return JWT令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}
