/**
 * 环境配置文件
 * 根据不同环境配置不同的API地址和其他配置
 */

// 环境类型
export type EnvType = 'development' | 'production' | 'test'

// 环境配置接口
interface EnvConfig {
  // API基础地址
  API_BASE_URL: string
  // 是否开启调试模式
  DEBUG: boolean
  // 应用名称
  APP_NAME: string
  // 应用版本
  APP_VERSION: string
  // 文件上传大小限制（MB）
  MAX_FILE_SIZE: number
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: number
}

// 获取当前环境
const getEnv = (): EnvType => {
  // 在uni-app中，可以通过process.env.NODE_ENV获取环境
  const env = process.env.NODE_ENV as EnvType
  return env || 'development'
}

// 不同环境的配置
const envConfigs: Record<EnvType, EnvConfig> = {
  // 开发环境
  development: {
    API_BASE_URL: '/api/', // 使用代理路径
    DEBUG: true,
    APP_NAME: '嘉依打卡',
    APP_VERSION: '1.0.0',
    MAX_FILE_SIZE: 10, // 10MB
    REQUEST_TIMEOUT: 10000 // 10秒
  },
  
  // 生产环境
  production: {
    API_BASE_URL: 'https://api.watertracker.com/api/v1/',
    DEBUG: false,
    APP_NAME: '嘉依打卡',
    APP_VERSION: '1.0.0',
    MAX_FILE_SIZE: 10, // 10MB
    REQUEST_TIMEOUT: 15000 // 15秒
  },
  
  // 测试环境
  test: {
    API_BASE_URL: 'https://test-api.watertracker.com/api/v1/',
    DEBUG: true,
    APP_NAME: '嘉依打卡-测试版',
    APP_VERSION: '1.0.0-test',
    MAX_FILE_SIZE: 5, // 5MB
    REQUEST_TIMEOUT: 8000 // 8秒
  }
}

// 获取当前环境配置
export const getEnvConfig = (): EnvConfig => {
  const env = getEnv()
  return envConfigs[env]
}

// 导出当前环境配置
export const ENV_CONFIG = getEnvConfig()

// 导出常用配置
export const {
  API_BASE_URL,
  DEBUG,
  APP_NAME,
  APP_VERSION,
  MAX_FILE_SIZE,
  REQUEST_TIMEOUT
} = ENV_CONFIG

// 工具函数：判断是否为开发环境
export const isDev = (): boolean => {
  return getEnv() === 'development'
}

// 工具函数：判断是否为生产环境
export const isProd = (): boolean => {
  return getEnv() === 'production'
}

// 工具函数：判断是否为测试环境
export const isTest = (): boolean => {
  return getEnv() === 'test'
}

// 工具函数：获取完整的API地址
export const getApiUrl = (path: string): string => {
  // 如果path已经是完整URL，直接返回
  if (path.startsWith('http')) {
    return path
  }
  
  // 确保path以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  
  return `${API_BASE_URL}${normalizedPath}`
}

// 调试日志函数
export const debugLog = (message: string, ...args: any[]): void => {
  if (DEBUG) {
    console.log(`[${APP_NAME}] ${message}`, ...args)
  }
}

// 错误日志函数
export const errorLog = (message: string, error?: any): void => {
  console.error(`[${APP_NAME}] ${message}`, error)
}

// 导出环境信息
export const ENV_INFO = {
  env: getEnv(),
  config: ENV_CONFIG,
  isDev: isDev(),
  isProd: isProd(),
  isTest: isTest()
}

// 在开发环境下打印环境信息
if (isDev()) {
  console.log('🌍 Environment Info:', ENV_INFO)
}
