package com.maike.api.couple.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 情侣关系实体类
 * 对应数据库表：couples
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("couples")
public class Couple implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 情侣关系ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 邀请码，用于情侣绑定
     */
    @TableField("invite_code")
    private String inviteCode;

    /**
     * 用户1的ID，发起绑定的用户
     */
    @TableField("user1_id")
    private String user1Id;

    /**
     * 用户2的ID，被邀请的用户
     */
    @TableField("user2_id")
    private String user2Id;

    /**
     * 绑定成功时间
     */
    @TableField("bound_at")
    private LocalDateTime boundAt;

    /**
     * 关系状态：1-活跃，0-已解绑
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 纪念日日期，可自定义设置
     */
    @TableField("anniversary_date")
    private LocalDate anniversaryDate;

    /**
     * 情侣昵称，如"小明&小红"
     */
    @TableField("couple_name")
    private String coupleName;

    /**
     * 在一起总天数，自动计算
     */
    @TableField("total_days")
    private Integer totalDays;

    /**
     * 记录状态：0-正常，1-停用，2-删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "2")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断关系是否活跃
     * 
     * @return true-活跃，false-不活跃
     */
    public boolean isActive() {
        return Boolean.TRUE.equals(this.isActive);
    }

    /**
     * 判断关系是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断关系是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "2".equals(this.status);
    }

    /**
     * 判断是否已绑定（有两个用户）
     * 
     * @return true-已绑定，false-未绑定
     */
    public boolean isBound() {
        return this.user1Id != null && this.user2Id != null && this.boundAt != null;
    }

    /**
     * 获取另一个用户的ID
     * 
     * @param currentUserId 当前用户ID
     * @return 另一个用户的ID
     */
    public String getPartnerUserId(String currentUserId) {
        if (currentUserId == null) {
            return null;
        }
        
        if (currentUserId.equals(this.user1Id)) {
            return this.user2Id;
        } else if (currentUserId.equals(this.user2Id)) {
            return this.user1Id;
        }
        
        return null;
    }

    /**
     * 判断用户是否属于这个情侣关系
     * 
     * @param userId 用户ID
     * @return true-属于，false-不属于
     */
    public boolean containsUser(String userId) {
        return userId != null && (userId.equals(this.user1Id) || userId.equals(this.user2Id));
    }

    /**
     * 计算在一起的天数
     * 
     * @return 在一起的天数
     */
    public long calculateTotalDays() {
        if (this.anniversaryDate == null) {
            return 0;
        }
        
        LocalDate now = LocalDate.now();
        return ChronoUnit.DAYS.between(this.anniversaryDate, now) + 1; // +1 包含当天
    }

    /**
     * 更新总天数
     */
    public void updateTotalDays() {
        this.totalDays = (int) calculateTotalDays();
    }

    /**
     * 获取状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusText() {
        if (!isNormal()) {
            return "已删除";
        }
        
        if (!isActive()) {
            return "已解绑";
        }
        
        if (!isBound()) {
            return "待绑定";
        }
        
        return "正常";
    }

    /**
     * 生成默认情侣昵称
     * 
     * @param user1Nickname 用户1昵称
     * @param user2Nickname 用户2昵称
     * @return 默认情侣昵称
     */
    public static String generateDefaultCoupleName(String user1Nickname, String user2Nickname) {
        if (user1Nickname == null || user2Nickname == null) {
            return "甜蜜情侣";
        }
        return user1Nickname + "&" + user2Nickname;
    }
}
