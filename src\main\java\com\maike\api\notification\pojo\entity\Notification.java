package com.maike.api.notification.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知实体类
 * 对应数据库表：notifications
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("notifications")
public class Notification implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 接收用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 通知类型：system-系统通知，achievement-成就通知，reminder-提醒通知，interaction-互动通知，challenge-挑战通知，couple-情侣通知
     */
    @TableField("type")
    private String type;

    /**
     * 通知标题
     */
    @TableField("title")
    private String title;

    /**
     * 通知内容
     */
    @TableField("message")
    private String message;

    /**
     * 附加数据，JSON格式存储相关信息
     */
    @TableField("data")
    private String data;

    /**
     * 是否已读：1-已读，0-未读
     */
    @TableField("is_read")
    private Boolean isRead;

    /**
     * 阅读时间
     */
    @TableField("read_at")
    private LocalDateTime readAt;

    /**
     * 优先级：low-低，normal-普通，high-高，urgent-紧急
     */
    @TableField("priority")
    private String priority;

    /**
     * 操作类型：click-点击，redirect-跳转，none-无操作
     */
    @TableField("action_type")
    private String actionType;

    /**
     * 操作链接，点击通知后跳转的页面
     */
    @TableField("action_url")
    private String actionUrl;

    /**
     * 发送者ID，用户间通知时使用
     */
    @TableField("sender_id")
    private String senderId;

    /**
     * 关联对象ID，如成就ID、视频ID等
     */
    @TableField("related_id")
    private String relatedId;

    /**
     * 关联对象类型：achievement、video、challenge等
     */
    @TableField("related_type")
    private String relatedType;

    /**
     * 过期时间，过期后自动删除
     */
    @TableField("expire_at")
    private LocalDateTime expireAt;

    /**
     * 是否推送：1-推送，0-不推送
     */
    @TableField("is_push")
    private Boolean isPush;

    /**
     * 推送时间
     */
    @TableField("push_at")
    private LocalDateTime pushAt;

    /**
     * 设备推送令牌，多个用逗号分隔
     */
    @TableField("device_tokens")
    private String deviceTokens;

    /**
     * 通知状态：0-正常，1-已删除，2-已过期
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断通知是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断通知是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.status);
    }

    /**
     * 判断通知是否已过期
     * 
     * @return true-已过期，false-未过期
     */
    public boolean isExpired() {
        return "2".equals(this.status) || (expireAt != null && LocalDateTime.now().isAfter(expireAt));
    }

    /**
     * 获取通知类型显示文本
     * 
     * @return 类型显示文本
     */
    public String getTypeText() {
        if (type == null) {
            return "系统通知";
        }
        
        switch (type) {
            case "system":
                return "系统通知";
            case "achievement":
                return "成就通知";
            case "reminder":
                return "提醒通知";
            case "interaction":
                return "互动通知";
            case "challenge":
                return "挑战通知";
            case "couple":
                return "情侣通知";
            default:
                return "系统通知";
        }
    }

    /**
     * 获取优先级显示文本
     * 
     * @return 优先级显示文本
     */
    public String getPriorityText() {
        if (priority == null) {
            return "普通";
        }
        
        switch (priority) {
            case "low":
                return "低";
            case "normal":
                return "普通";
            case "high":
                return "高";
            case "urgent":
                return "紧急";
            default:
                return "普通";
        }
    }

    /**
     * 获取优先级颜色
     * 
     * @return 优先级颜色
     */
    public String getPriorityColor() {
        if (priority == null) {
            return "#1890ff";
        }
        
        switch (priority) {
            case "low":
                return "#52c41a"; // 绿色
            case "normal":
                return "#1890ff"; // 蓝色
            case "high":
                return "#fa8c16"; // 橙色
            case "urgent":
                return "#f5222d"; // 红色
            default:
                return "#1890ff";
        }
    }

    /**
     * 标记为已读
     */
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
    }

    /**
     * 判断是否为系统通知
     * 
     * @return true-系统通知，false-非系统通知
     */
    public boolean isSystemNotification() {
        return "system".equals(this.type);
    }

    /**
     * 判断是否为成就通知
     * 
     * @return true-成就通知，false-非成就通知
     */
    public boolean isAchievementNotification() {
        return "achievement".equals(this.type);
    }

    /**
     * 判断是否为提醒通知
     * 
     * @return true-提醒通知，false-非提醒通知
     */
    public boolean isReminderNotification() {
        return "reminder".equals(this.type);
    }

    /**
     * 判断是否为互动通知
     * 
     * @return true-互动通知，false-非互动通知
     */
    public boolean isInteractionNotification() {
        return "interaction".equals(this.type);
    }

    /**
     * 判断是否为挑战通知
     * 
     * @return true-挑战通知，false-非挑战通知
     */
    public boolean isChallengeNotification() {
        return "challenge".equals(this.type);
    }

    /**
     * 判断是否为情侣通知
     * 
     * @return true-情侣通知，false-非情侣通知
     */
    public boolean isCoupleNotification() {
        return "couple".equals(this.type);
    }
}
