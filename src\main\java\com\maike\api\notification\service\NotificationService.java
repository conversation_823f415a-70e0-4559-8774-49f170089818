package com.maike.api.notification.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.notification.pojo.entity.Notification;
import com.maike.api.notification.pojo.vo.NotificationVO;

import java.util.List;

/**
 * 通知服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface NotificationService extends IService<Notification> {

    /**
     * 分页获取通知列表
     * 
     * @param userId 用户ID
     * @param type   通知类型（可选）
     * @param isRead 是否已读（可选）
     * @param page   页码
     * @param size   每页数量
     * @return 分页结果
     */
    IPage<NotificationVO> getNotificationList(String userId, String type, Boolean isRead, Integer page, Integer size);

    /**
     * 获取未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读通知数量
     */
    Integer getUnreadCount(String userId);

    /**
     * 标记通知为已读
     * 
     * @param userId         用户ID
     * @param notificationId 通知ID
     */
    void markAsRead(String userId, String notificationId);

    /**
     * 批量标记通知为已读
     * 
     * @param userId          用户ID
     * @param notificationIds 通知ID列表
     */
    void batchMarkAsRead(String userId, List<String> notificationIds);

    /**
     * 标记所有通知为已读
     * 
     * @param userId 用户ID
     */
    void markAllAsRead(String userId);

    /**
     * 删除通知
     * 
     * @param userId         用户ID
     * @param notificationId 通知ID
     */
    void deleteNotification(String userId, String notificationId);

    /**
     * 批量删除通知
     * 
     * @param userId          用户ID
     * @param notificationIds 通知ID列表
     */
    void batchDeleteNotifications(String userId, List<String> notificationIds);

    /**
     * 创建系统通知
     * 
     * @param userId    用户ID
     * @param title     标题
     * @param message   内容
     * @param priority  优先级
     * @param data      附加数据
     */
    void createSystemNotification(String userId, String title, String message, String priority, String data);

    /**
     * 创建成就通知
     * 
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @param title         标题
     * @param message       内容
     */
    void createAchievementNotification(String userId, String achievementId, String title, String message);

    /**
     * 创建互动通知
     * 
     * @param userId     用户ID
     * @param senderId   发送者ID
     * @param relatedId  关联对象ID
     * @param title      标题
     * @param message    内容
     * @param actionUrl  操作链接
     */
    void createInteractionNotification(String userId, String senderId, String relatedId, String title, String message, String actionUrl);

    /**
     * 创建挑战通知
     * 
     * @param userId      用户ID
     * @param challengeId 挑战ID
     * @param title       标题
     * @param message     内容
     */
    void createChallengeNotification(String userId, String challengeId, String title, String message);

    /**
     * 创建情侣通知
     * 
     * @param userId   用户ID
     * @param senderId 发送者ID
     * @param title    标题
     * @param message  内容
     */
    void createCoupleNotification(String userId, String senderId, String title, String message);

    /**
     * 获取通知统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    NotificationStatsVO getNotificationStats(String userId);

    /**
     * 清理过期通知
     */
    void cleanExpiredNotifications();

    /**
     * 通知统计VO
     */
    class NotificationStatsVO {
        private Integer totalCount;
        private Integer unreadCount;
        private Integer systemCount;
        private Integer achievementCount;
        private Integer interactionCount;

        // getters and setters
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public Integer getUnreadCount() { return unreadCount; }
        public void setUnreadCount(Integer unreadCount) { this.unreadCount = unreadCount; }
        public Integer getSystemCount() { return systemCount; }
        public void setSystemCount(Integer systemCount) { this.systemCount = systemCount; }
        public Integer getAchievementCount() { return achievementCount; }
        public void setAchievementCount(Integer achievementCount) { this.achievementCount = achievementCount; }
        public Integer getInteractionCount() { return interactionCount; }
        public void setInteractionCount(Integer interactionCount) { this.interactionCount = interactionCount; }
    }
}
