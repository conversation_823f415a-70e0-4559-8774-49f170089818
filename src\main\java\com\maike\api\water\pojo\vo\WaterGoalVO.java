package com.maike.api.water.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 喝水目标响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class WaterGoalVO {

    /**
     * 目标ID
     */
    private String goalId;

    /**
     * 每日目标喝水量，单位毫升
     */
    private Integer dailyGoalMl;

    /**
     * 目标日期
     */
    private LocalDate goalDate;

    /**
     * 实际完成量，单位毫升
     */
    private Integer actualAmountMl;

    /**
     * 是否达成目标
     */
    private Boolean isAchieved;

    /**
     * 达成目标的时间
     */
    private LocalDateTime achievementTime;

    /**
     * 完成率，百分比
     */
    private BigDecimal completionRate;

    /**
     * 完成率百分比（整数）
     */
    private Integer completionRatePercent;

    /**
     * 剩余需要喝水量（毫升）
     */
    private Integer remainingAmount;

    /**
     * 是否超额完成
     */
    private Boolean isOverAchieved;
}
