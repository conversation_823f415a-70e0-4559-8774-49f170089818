package com.maike.api.couple.pojo.vo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 情侣信息响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleInfoVO {

    /**
     * 情侣关系ID
     */
    private String coupleId;

    /**
     * 情侣昵称
     */
    private String coupleName;

    /**
     * 纪念日日期
     */
    private LocalDate anniversaryDate;

    /**
     * 在一起总天数
     */
    private Integer totalDays;

    /**
     * 关系是否活跃
     */
    private Boolean isActive;

    /**
     * 绑定时间
     */
    private LocalDateTime boundAt;

    /**
     * 伴侣信息
     */
    private PartnerInfo partnerInfo;

    /**
     * 伴侣信息内部类
     */
    @Data
    public static class PartnerInfo {
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 头像URL
         */
        private String avatarUrl;

        /**
         * 性别
         */
        private String gender;
    }
}
