package com.maike.api.common.config;

import com.maike.api.common.interceptor.JwtInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 配置拦截器、跨域等
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final JwtInterceptor jwtInterceptor;

    /**
     * 配置拦截器
     * 
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/v1/**") // 拦截所有API请求
                .excludePathPatterns(
                        // 排除不需要认证的接口
                        "/api/v1/user",              // 用户注册
                        "/api/v1/user/login",        // 用户登录
                        "/api/v1/user/sendCode",     // 发送验证码
                        "/api/v1/health",            // 健康检查
                        "/api/v1/doc.html",          // API文档
                        "/api/v1/swagger-ui/**",     // Swagger UI
                        "/api/v1/v3/api-docs/**"     // OpenAPI文档
                );
    }

    /**
     * 配置跨域
     * 
     * @param registry 跨域注册器
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/v1/**")
                .allowedOriginPatterns("*") // 允许所有域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许的HTTP方法
                .allowedHeaders("*") // 允许所有请求头
                .allowCredentials(true) // 允许携带凭证
                .maxAge(3600); // 预检请求的缓存时间（秒）
    }
}
