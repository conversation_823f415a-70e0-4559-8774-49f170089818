<template>
  <view class="page">
    <!-- 顶部欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-header">
        <image class="avatar" :src="userInfo.avatar_url || '/static/default-avatar.png'" mode="aspectFill"></image>
        <view class="welcome-text">
          <text class="greeting">{{ greeting }}</text>
          <text class="username">{{ userInfo.nickname || '小可爱' }}</text>
        </view>
        <view class="weather-info">
          <text class="temperature">22°C</text>
          <text class="weather">晴天</text>
        </view>
      </view>
    </view>

    <!-- 今日进度卡片 -->
    <view class="progress-card">
      <view class="card-header">
        <text class="card-title">今日喝水进度</text>
        <text class="progress-text">{{ todayProgress.completion_rate }}%</text>
      </view>

      <!-- 进度环 -->
      <view class="progress-ring-container">
        <view class="progress-ring">
          <view class="progress-circle" :style="{ transform: `rotate(${todayProgress.completion_rate * 3.6}deg)` }"></view>
          <view class="progress-inner">
            <text class="current-amount">{{ todayProgress.today_total }}</text>
            <text class="unit">ml</text>
            <text class="target">目标 {{ todayProgress.daily_goal }}ml</text>
          </view>
        </view>
      </view>

      <!-- 快捷打卡按钮 -->
      <view class="quick-actions">
        <view class="cup-buttons">
          <view class="cup-btn" @click="quickDrink(200)">
            <image src="/static/cup-small.png" class="cup-icon"></image>
            <text>200ml</text>
          </view>
          <view class="cup-btn" @click="quickDrink(300)">
            <image src="/static/cup-medium.png" class="cup-icon"></image>
            <text>300ml</text>
          </view>
          <view class="cup-btn" @click="quickDrink(500)">
            <image src="/static/cup-large.png" class="cup-icon"></image>
            <text>500ml</text>
          </view>
        </view>
        <button class="custom-btn" @click="goToWaterPage">自定义打卡</button>
      </view>
    </view>

    <!-- 情侣互动区域 -->
    <view class="couple-section" v-if="coupleInfo.couple_id">
      <view class="section-header">
        <text class="section-title">情侣互动</text>
        <text class="more-btn" @click="goToCoupleePage">查看更多</text>
      </view>

      <view class="couple-progress">
        <view class="partner-progress">
          <image class="partner-avatar" :src="coupleInfo.partner_info.avatar_url" mode="aspectFill"></image>
          <view class="partner-info">
            <text class="partner-name">{{ coupleInfo.partner_info.nickname }}</text>
            <text class="partner-status">今日已喝水 {{ partnerProgress.today_total }}ml</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: partnerProgress.completion_rate + '%' }"></view>
          </view>
        </view>

        <button class="remind-btn" @click="sendReminder">提醒TA喝水</button>
      </view>
    </view>

    <!-- 最近动态 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近动态</text>
        <text class="more-btn" @click="goToVideoPage">查看更多</text>
      </view>

      <view class="recent-videos">
        <view class="video-item" v-for="video in recentVideos" :key="video.id" @click="playVideo(video)">
          <image class="video-thumbnail" :src="video.thumbnail_url" mode="aspectFill"></image>
          <view class="video-info">
            <text class="video-desc">{{ video.description }}</text>
            <text class="video-time">{{ formatTime(video.create_time) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import request from '@/utils/request'

// 响应式数据定义
const userInfo = ref({
  nickname: '',
  avatar_url: ''
})

const todayProgress = ref({
  today_total: 0,
  daily_goal: 2000,
  completion_rate: 0,
  is_achieved: false
})

const coupleInfo = ref({
  couple_id: null,
  partner_info: {
    nickname: '',
    avatar_url: ''
  }
})

const partnerProgress = ref({
  today_total: 0,
  completion_rate: 0
})

const recentVideos = ref([])

// 计算属性：根据时间显示问候语
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 页面加载时获取数据
onMounted(() => {
  loadUserInfo()
  loadTodayProgress()
  loadCoupleInfo()
  loadRecentVideos()
})

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await request.get('/user')
    userInfo.value = response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取今日进度
const loadTodayProgress = async () => {
  try {
    const response = await request.get('/water/todayProgress')
    todayProgress.value = response.data
  } catch (error) {
    console.error('获取今日进度失败:', error)
  }
}

// 获取情侣信息
const loadCoupleInfo = async () => {
  try {
    const response = await request.get('/couple')
    coupleInfo.value = response.data
  } catch (error) {
    console.error('获取情侣信息失败:', error)
  }
}

// 获取最近视频
const loadRecentVideos = async () => {
  try {
    const response = await request.get('/video', { type: 'couple', size: 3 })
    recentVideos.value = response.data.videos
  } catch (error) {
    console.error('获取最近视频失败:', error)
  }
}

// 快捷喝水打卡
const quickDrink = async (amount: number) => {
  try {
    const response = await request.post('/water', {
      amount_ml: amount,
      cup_type: amount === 200 ? 'small' : amount === 300 ? 'medium' : 'large',
      recorded_at: new Date().toISOString()
    })

    // 更新进度显示
    todayProgress.value = {
      ...todayProgress.value,
      today_total: response.data.today_total,
      completion_rate: response.data.goal_progress,
      is_achieved: response.data.is_goal_achieved
    }

    uni.showToast({
      title: '打卡成功！',
      icon: 'success'
    })
  } catch (error) {
    console.error('打卡失败:', error)
  }
}

// 发送提醒给情侣
const sendReminder = async () => {
  try {
    await request.post('/couple/remind', {
      message: '该喝水啦，宝贝！',
      type: 'water_reminder'
    })

    uni.showToast({
      title: '提醒发送成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('发送提醒失败:', error)
  }
}

// 页面跳转方法
const goToWaterPage = () => {
  uni.switchTab({ url: '/pages/water/water' })
}

const goToCoupleePage = () => {
  uni.switchTab({ url: '/pages/couple/couple' })
}

const goToVideoPage = () => {
  uni.switchTab({ url: '/pages/video/video' })
}

const playVideo = (video: any) => {
  uni.navigateTo({
    url: `/pages/video/detail?id=${video.id}`
  })
}

// 格式化时间显示
const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return `${Math.floor(minutes / 1440)}天前`
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
  padding: 0 32rpx;
}

/* 顶部欢迎区域样式 */
.welcome-section {
  padding: 40rpx 0;
}

.welcome-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid #FFB6C1;
}

.welcome-text {
  flex: 1;
  margin-left: 24rpx;
}

.greeting {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
}

.weather-info {
  text-align: right;
}

.temperature {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.weather {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 进度卡片样式 */
.progress-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 12rpx 48rpx rgba(255, 182, 193, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.progress-text {
  font-size: 28rpx;
  color: #FF69B4;
  font-weight: bold;
}

.progress-ring-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.progress-ring {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: conic-gradient(#FF69B4 0deg, #FFE4E1 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(#FF69B4 0deg, #FFE4E1 0deg);
  transition: transform 0.3s ease;
}

.progress-inner {
  position: relative;
  z-index: 2;
  background: white;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.2);
}

.current-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  line-height: 1;
}

.unit {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.target {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 快捷操作样式 */
.quick-actions {
  text-align: center;
}

.cup-buttons {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
}

.cup-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
  border-radius: 20rpx;
  min-width: 120rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
}

.cup-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 182, 193, 0.25);
}

.cup-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.cup-btn text {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
}

.custom-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
}

.custom-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-btn {
  font-size: 24rpx;
  color: #FF69B4;
}

/* 情侣互动区域样式 */
.couple-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.couple-progress {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.partner-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.partner-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
}

.partner-info {
  flex: 1;
}

.partner-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.partner-status {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.progress-bar {
  width: 120rpx;
  height: 8rpx;
  background: #FFE4E1;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.remind-btn {
  background: linear-gradient(135deg, #87CEEB, #B0E0E6);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  align-self: flex-start;
}

/* 最近动态样式 */
.recent-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.recent-videos {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.video-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background: #FFF0F5;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.video-item:active {
  transform: scale(0.98);
  background: #FFE4E1;
}

.video-thumbnail {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
}

.video-info {
  flex: 1;
}

.video-desc {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.video-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}
</style>
