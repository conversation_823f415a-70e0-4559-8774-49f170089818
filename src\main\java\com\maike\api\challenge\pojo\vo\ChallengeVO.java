package com.maike.api.challenge.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 挑战响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ChallengeVO {

    /**
     * 挑战ID
     */
    private String id;

    /**
     * 挑战名称
     */
    private String name;

    /**
     * 挑战描述
     */
    private String description;

    /**
     * 挑战类型：water-喝水挑战，video-视频挑战，interaction-互动挑战，custom-自定义
     */
    private String type;

    /**
     * 挑战类型显示文本
     */
    private String typeText;

    /**
     * 挑战持续天数
     */
    private Integer durationDays;

    /**
     * 挑战开始日期
     */
    private LocalDate startDate;

    /**
     * 挑战结束日期
     */
    private LocalDate endDate;

    /**
     * 挑战状态：pending-待开始，active-进行中，completed-已完成，failed-已失败，cancelled-已取消
     */
    private String status;

    /**
     * 挑战状态显示文本
     */
    private String statusText;

    /**
     * 用户1的完成进度，百分比
     */
    private BigDecimal user1Progress;

    /**
     * 用户2的完成进度，百分比
     */
    private BigDecimal user2Progress;

    /**
     * 总体完成进度，百分比
     */
    private BigDecimal totalProgress;

    /**
     * 奖励类型：achievement-成就，theme-主题，title-称号
     */
    private String rewardType;

    /**
     * 奖励内容
     */
    private String rewardValue;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 创建挑战的用户ID
     */
    private String createdByUser;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 剩余天数
     */
    private Long remainingDays;

    /**
     * 已进行天数
     */
    private Long elapsedDays;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 情侣信息
     */
    private CoupleInfo coupleInfo;

    /**
     * 情侣信息内部类
     */
    @Data
    public static class CoupleInfo {
        /**
         * 情侣ID
         */
        private String coupleId;

        /**
         * 用户1信息
         */
        private UserInfo user1;

        /**
         * 用户2信息
         */
        private UserInfo user2;
    }

    /**
     * 用户信息内部类
     */
    @Data
    public static class UserInfo {
        /**
         * 用户ID
         */
        private String userId;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 头像URL
         */
        private String avatarUrl;

        /**
         * 性别
         */
        private String gender;
    }
}
