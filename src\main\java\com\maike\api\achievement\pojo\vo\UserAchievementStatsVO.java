package com.maike.api.achievement.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * 用户成就统计响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class UserAchievementStatsVO {

    /**
     * 总成就数量
     */
    private Integer totalAchievements;

    /**
     * 已解锁成就数量
     */
    private Integer unlockedCount;

    /**
     * 总积分
     */
    private Integer totalPoints;

    /**
     * 解锁率（百分比）
     */
    private Double unlockRate;

    /**
     * 最近解锁的成就
     */
    private List<RecentUnlockedAchievement> recentUnlocked;

    /**
     * 各分类统计
     */
    private CategoryStats categoryStats;

    /**
     * 各稀有度统计
     */
    private RarityStats rarityStats;

    /**
     * 最近解锁的成就内部类
     */
    @Data
    public static class RecentUnlockedAchievement {
        /**
         * 成就ID
         */
        private String achievementId;

        /**
         * 成就名称
         */
        private String name;

        /**
         * 成就图标URL
         */
        private String iconUrl;

        /**
         * 稀有度
         */
        private String rarity;

        /**
         * 稀有度颜色
         */
        private String rarityColor;

        /**
         * 解锁时间
         */
        private String unlockedAt;

        /**
         * 积分
         */
        private Integer points;
    }

    /**
     * 分类统计内部类
     */
    @Data
    public static class CategoryStats {
        /**
         * 喝水相关成就统计
         */
        private CategoryStat water;

        /**
         * 视频相关成就统计
         */
        private CategoryStat video;

        /**
         * 社交相关成就统计
         */
        private CategoryStat social;
    }

    /**
     * 单个分类统计内部类
     */
    @Data
    public static class CategoryStat {
        /**
         * 总数量
         */
        private Integer total;

        /**
         * 已解锁数量
         */
        private Integer unlocked;

        /**
         * 解锁率
         */
        private Double unlockRate;
    }

    /**
     * 稀有度统计内部类
     */
    @Data
    public static class RarityStats {
        /**
         * 普通成就统计
         */
        private RarityStat common;

        /**
         * 稀有成就统计
         */
        private RarityStat rare;

        /**
         * 史诗成就统计
         */
        private RarityStat epic;

        /**
         * 传说成就统计
         */
        private RarityStat legendary;
    }

    /**
     * 单个稀有度统计内部类
     */
    @Data
    public static class RarityStat {
        /**
         * 总数量
         */
        private Integer total;

        /**
         * 已解锁数量
         */
        private Integer unlocked;

        /**
         * 解锁率
         */
        private Double unlockRate;
    }
}
