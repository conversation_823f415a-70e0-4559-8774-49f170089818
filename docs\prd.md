# 产品需求文档 (PRD)

**项目名称：** 嘉依打卡：元气水杯与Ta的约定

**版本：** V1.1

**日期：** 2025年7月30日

---

## 1. 引言

### 1.1 文档目的

本文档旨在明确“嘉依打卡”APP的产品目标、功能范围、核心用户需求、技术栈选择以及关键绩效指标（KPIs）。它将作为项目所有参与方（产品、设计、开发、测试等）的共同参考，确保产品开发过程中的方向一致性和效率。

### 1.2 产品概述

“嘉依打卡”是一款专为情侣或亲密关系设计的喝水打卡应用。它通过可爱、粉嫩的视觉风格，以及独特的视频打卡和双向互动功能，旨在帮助用户（特别是情侣间）共同养成健康的饮水习惯，并通过互相监督、鼓励的方式，增进彼此的健康联结与情感互动。

### 1.3 目标用户

* **核心用户：** 希望通过互动和趣味方式，与伴侣（例如：用户本人和女朋友）共同关注健康，并互相监督、鼓励养成饮水习惯的情侣或亲密关系。
* **次要用户：** 追求可爱、时尚风格，并乐于通过视频分享生活和互动的年轻用户；希望共同养成良好饮水习惯，并从中获得情感联结和乐趣的用户。

### 1.4 用户画像示例

* **姓名：** 嘉依 (虚拟用户，代指女朋友)
    * **年龄：** 20-28岁
    * **职业：** 学生/初入职场的白领
    * **生活方式：** 关注健康与生活品质，喜欢尝试新鲜事物，乐于在社交媒体分享日常，追求生活中的“小确幸”和仪式感。
    * **痛点：** 容易忘记喝水，觉得喝水枯燥，需要外部提醒和激励。
    * **期待：** 希望APP可爱好看，操作简单，能和男友一起打卡，互相监督，获得成就感和甜蜜感。
* **姓名：** 小明 (虚拟用户，代指男朋友)
    * **年龄：** 20-30岁
    * **职业：** 学生/职场新人
    * **生活方式：** 关心女朋友的健康，愿意为她付出，希望通过共同的活动增进感情。
    * **痛点：** 提醒女朋友喝水时，方式生硬，容易被嫌弃；希望有更趣味、更不打扰的方式提醒和监督。
    * **期待：** APP能帮助他更好地监督和鼓励女朋友喝水，同时也能让自己养成好习惯，增加两人互动乐趣。

### 1.5 假设与约束

* **假设：** 用户愿意通过短视频形式分享喝水瞬间；情侣用户对共享喝水数据有较高接受度。
* **约束：** 移动端APP（iOS/Android）；视频时长和存储容量限制；需符合相关内容审核法规。

## 2. 产品目标与价值

### 2.1 核心目标

1.  提升用户（尤其是情侣）的每日饮水量，帮助养成健康饮水习惯。
2.  通过视频打卡和社交互动，增强用户对APP的粘性和使用乐趣。
3.  加深情侣间的健康联结和情感互动，打造专属的健康生活方式。

### 2.2 产品价值主张

“嘉依打卡”通过定制化的粉嫩可爱风格和独特的情侣互动功能，将枯燥的喝水行为转化为充满趣味和甜蜜的健康约定，让喝水打卡成为情侣间增进感情的日常仪式。

## 3. 功能需求 (Functional Requirements)

### 3.1 用户与账户管理

* **3.1.1 注册/登录：**
    * 支持手机号/验证码注册登录。
    * 支持第三方社交账号（如微信）快速登录。
    * 用户协议与隐私政策提示。
* **3.1.2 个人资料管理：**
    * 设置/修改头像、昵称、性别、生日。
    * 个性签名设置。
* **3.1.3 情侣绑定：**
    * 通过唯一邀请码/链接邀请对方绑定。
    * 绑定后双方可互见对方信息及核心打卡数据。
    * 解除绑定功能。
* **3.1.4 消息通知：**
    * 系统消息（成就解锁、活动通知）。
    * 互动消息（点赞、评论、提醒）。

### 3.2 喝水打卡与进度管理

* **3.2.1 每日饮水目标设置：**
    * 自定义每日饮水目标（如2000ml）。
    * 可选择默认饮水目标（根据性别、体重推荐）。
* **3.2.2 饮水量记录：**
    * 快捷打卡：点击预设杯型（如200ml、300ml），一键添加。
    * 自定义输入：手动输入饮水毫升数。
    * 记录时间：自动记录打卡时间，支持手动修改。
    * 撤销/修改当日打卡记录。
* **3.2.3 智能喝水提醒：**
    * 开启/关闭提醒总开关。
    * 自定义提醒时间段（如9:00-22:00）。
    * 自定义提醒频率（如每隔1小时）。
    * 自定义提醒音效（可爱泡泡音、元气少女音等）。
    * 自定义提醒文案（支持情侣专属文案模板）。
* **3.2.4 饮水进度可视化：**
    * **首页个人进度条/环：** 直观展示今日饮水目标达成比例。
    * **情侣双人进度：** 首页并列展示你和女朋友的今日饮水进度。
    * **日/周/月/年数据统计：**
        * 折线图/柱状图展示每日/每周/每月平均饮水量。
        * 目标达成天数统计。
        * 历史打卡记录列表。

### 3.3 视频打卡与分享

* **3.3.1 视频拍摄：**
    * APP内调用摄像头，支持前后置切换。
    * 支持5-15秒短视频拍摄。
    * 基础拍摄功能：倒计时、闪光灯。
* **3.3.2 视频编辑：**
    * **滤镜：** 多种可爱、粉嫩风格滤镜选择。
    * **贴纸/挂件：** 动态/静态可爱贴纸，支持缩放、旋转、拖拽。
    * **文字/画笔：** 添加文字、涂鸦。
    * **背景音乐：** 热门可爱风格背景音乐库，支持音量调节。
    * **视频剪辑：** 简单的裁剪、片段调整。
* **3.3.3 视频上传与发布：**
    * 填写视频描述/心情标签。
    * 选择视频可见范围（仅自己、情侣可见、公开）。
    * 上传至云端服务器。
* **3.3.4 视频动态流：**
    * 首页展示情侣动态流：按照时间线展示你和女朋友上传的喝水视频。
    * 点赞、评论功能。
    * 分享功能（分享至微信好友/朋友圈）。
    * 举报功能（针对违规视频）。
    * 视频播放器：支持全屏播放、暂停、进度条。

### 3.4 社交互动（情侣专属）

* **3.4.1 互相提醒喝水：**
    * 在情侣页面或对方主页，可一键向对方发送喝水提醒（带有可爱文案或表情）。
    * 提醒发送成功后有反馈。
* **3.4.2 共同饮水挑战：**
    * 发起/参与情侣专属饮水挑战（如：连续7天每日喝水达标）。
    * 挑战进度实时更新，双方可见。
    * 完成挑战可获得情侣专属奖励（如：限定主题、特殊成就）。
* **3.4.3 评论与点赞：**
    * 情侣间可对彼此的喝水视频进行评论、点赞。
    * 评论支持文字、表情。

### 3.5 激励与成就系统

* **3.5.1 个人成就体系：**
    * 设定多种个人成就（如：首次打卡、连续打卡7天、总饮水量达标等）。
    * 成就解锁后获得虚拟勋章。
    * 勋章墙展示已获得的勋章。
* **3.5.2 情侣专属成就：**
    * 完成情侣共同挑战或累积互动次数可获得情侣专属成就和勋章。
* **3.5.3 签到奖励：**
    * 每日签到，领取小奖励或积分。

### 3.6 个性化与主题

* **3.6.1 APP主题：**
    * 提供多套粉嫩可爱风格主题，支持用户切换。
    * 主题包含背景、图标、配色方案等。
* **3.6.2 自定义杯子：**
    * 选择喜欢的杯子样式（可解锁更多）。

## 4. 非功能性需求 (Non-Functional Requirements)

### 4.1 性能

* **加载速度：** APP启动时间小于3秒；页面切换响应时间小于1秒。
* **视频处理：** 视频上传成功率99%以上；视频播放流畅，卡顿率低于1%。
* **数据同步：** 喝水打卡数据和情侣互动数据实时同步，延迟低于1秒。
* **并发：** 支持至少10000并发用户同时在线操作。

### 4.2 安全性

* **数据加密：** 用户敏感数据（密码、手机号）采用强加密存储和传输。
* **API安全：** 所有API接口采用HTTPS协议，进行身份验证和授权。
* **内容审核：** 对用户上传的视频内容进行初步的AI/关键词审核，识别并处理违规内容。
* **账号安全：** 具备密码找回、手机验证、防暴力破解等机制。

### 4.3 可用性与用户体验 (UX)

* **视觉风格：** 严格遵循“粉嫩可爱”的视觉设计指南，所有UI元素保持一致性。
* **交互体验：** 操作流程简洁直观，减少用户认知负荷；核心功能（打卡、视频上传）路径最短。
* **反馈机制：** 用户操作后有即时、明确的反馈（成功提示、错误提示、加载状态）。
* **易学性：** 新用户能快速理解APP的核心功能和操作。
* **无障碍设计：** 考虑部分基础无障碍特性（如字体大小调整）。

### 4.4 兼容性

* **操作系统：** 支持iOS 12及以上版本；Android 8.0及以上版本。
* **设备兼容：** 适配主流手机型号和屏幕分辨率。
* **网络环境：** 在2G/3G/4G/5G/Wi-Fi网络环境下均能正常使用。

### 4.5 可扩展性

* **架构设计：** 采用模块化、分层架构，便于未来功能迭代和业务扩展。
* **API设计：** 接口设计具有通用性，方便第三方集成或未来多平台支持。
* **数据库：** 数据库结构设计合理，可支持大量用户和数据增长。

### 4.6 维护性

* 代码规范：遵循团队代码开发规范。
* 文档：提供详细的API文档、开发文档和测试用例。
* 日志监控：完善的日志记录和监控系统，便于问题排查和性能分析。

## 5. 埋点与数据指标 (Metrics / KPIs)

* **核心指标：**
    * 注册用户数 (Registered Users)
    * 情侣绑定成功率 (Couple Binding Success Rate)
    * 日活跃用户 (DAU) / 月活跃用户 (MAU)
    * 次日留存率 (Day 1 Retention Rate) / 7日留存率 (Day 7 Retention Rate)
    * 每日平均喝水打卡次数 (Avg. Daily Water Check-in Count)
    * 每日喝水目标达成率 (Daily Water Goal Achievement Rate)
    * 视频上传量 (Video Upload Count)
    * 视频播放量 (Video Play Count)
    * 情侣互动次数 (Couple Interaction Count - 点赞/评论/提醒)
* **用户行为指标：**
    * 各功能模块使用率（如：提醒设置使用率、成就系统查看率）。
    * 各主题/滤镜使用率。
* **性能指标：**
    * APP崩溃率。
    * 页面加载时长。

## 6. 商业化（初步考虑，待详细探讨）

* **增值服务：**
    * 付费解锁更多高级主题、独家滤镜、特色贴纸。
    * 情侣专属虚拟礼物赠送（如虚拟鲜花、特殊饮水动画）。
* **品牌合作：**
    * 与健康饮品、水杯品牌进行联名活动或广告植入。

## 7. 未来迭代展望 (Roadmap)

* **V1.2：** 引入更多健康数据记录（如体重、运动数据），实现更全面的健康管理。
* **V1.3：** 虚拟宠物/水杯养成系统，通过喝水打卡喂养宠物或升级水杯，增加用户粘性。
* **V1.4：** 语音打卡功能，提升便捷性。
* **V1.5：** 外部设备联动（如智能水杯），自动记录饮水量。