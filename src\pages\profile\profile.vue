<template>
  <view class="profile-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-header">
        <view class="avatar-section" @click="changeAvatar">
          <image class="avatar" :src="userInfo.avatar_url || '/static/default-avatar.png'" mode="aspectFill"></image>
          <view class="avatar-edit">
            <image class="edit-icon" src="/static/icons/camera.png"></image>
          </view>
        </view>
        
        <view class="user-info">
          <text class="username">{{ userInfo.nickname || '未设置昵称' }}</text>
          <text class="user-id">ID: {{ userInfo.id }}</text>
          <text class="join-date">{{ formatJoinDate(userInfo.create_time) }}</text>
        </view>
        
        <view class="edit-profile" @click="editProfile">
          <image class="edit-icon" src="/static/icons/edit.png"></image>
        </view>
      </view>
      
      <!-- 情侣状态 -->
      <view class="couple-status" v-if="coupleInfo.couple_id">
        <view class="couple-info">
          <image class="partner-avatar" :src="coupleInfo.partner_info.avatar_url" mode="aspectFill"></image>
          <view class="couple-text">
            <text class="couple-name">{{ coupleInfo.couple_name }}</text>
            <text class="together-days">在一起 {{ coupleInfo.total_days }} 天</text>
          </view>
        </view>
      </view>
      
      <view class="no-couple" v-else @click="bindCouple">
        <image class="heart-icon" src="/static/icons/heart-empty.png"></image>
        <text>点击绑定情侣关系</text>
      </view>
    </view>

    <!-- 数据统计卡片 -->
    <view class="stats-card">
      <view class="card-title">
        <text>我的数据</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ userStats.total_water_days }}</text>
          <text class="stat-label">打卡天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userStats.total_water_amount }}</text>
          <text class="stat-label">总喝水量(L)</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userStats.achievement_count }}</text>
          <text class="stat-label">获得成就</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userStats.video_count }}</text>
          <text class="stat-label">分享视频</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="goToStatistics">
          <view class="menu-left">
            <image class="menu-icon" src="/static/icons/chart.png"></image>
            <text class="menu-text">数据统计</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
        
        <view class="menu-item" @click="goToAchievements">
          <view class="menu-left">
            <image class="menu-icon" src="/static/icons/trophy.png"></image>
            <text class="menu-text">我的成就</text>
          </view>
          <view class="menu-badge" v-if="newAchievementCount > 0">
            <text>{{ newAchievementCount }}</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
        
        <view class="menu-item" @click="goToChallenges">
          <view class="menu-left">
            <image class="menu-icon" src="/static/icons/target.png"></image>
            <text class="menu-text">挑战任务</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="goToSettings">
          <view class="menu-left">
            <image class="menu-icon" src="/static/icons/settings.png"></image>
            <text class="menu-text">设置</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
        
        <view class="menu-item" @click="goToHelp">
          <view class="menu-left">
            <image class="menu-icon" src="/static/icons/help.png"></image>
            <text class="menu-text">帮助与反馈</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
        
        <view class="menu-item" @click="goToAbout">
          <view class="menu-left">
            <image class="menu-icon" src="/static/icons/info.png"></image>
            <text class="menu-text">关于我们</text>
          </view>
          <image class="arrow-icon" src="/static/icons/arrow-right.png"></image>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

// 响应式数据
const userInfo = ref({
  id: '',
  nickname: '',
  avatar_url: '',
  create_time: ''
})

const coupleInfo = ref({
  couple_id: null,
  couple_name: '',
  total_days: 0,
  partner_info: {
    nickname: '',
    avatar_url: ''
  }
})

const userStats = ref({
  total_water_days: 0,
  total_water_amount: 0,
  achievement_count: 0,
  video_count: 0
})

const newAchievementCount = ref(0)

// 页面加载时获取数据
onMounted(() => {
  loadUserInfo()
  loadCoupleInfo()
  loadUserStats()
})

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await request.get('/user')
    userInfo.value = response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取情侣信息
const loadCoupleInfo = async () => {
  try {
    const response = await request.get('/couple')
    coupleInfo.value = response.data
  } catch (error) {
    console.error('获取情侣信息失败:', error)
  }
}

// 获取用户统计数据
const loadUserStats = async () => {
  try {
    // 这里可以调用多个接口获取统计数据
    const [waterStats, achievements, videos] = await Promise.all([
      request.get('/water/statistics', { type: 'all' }),
      request.get('/achievement/my'),
      request.get('/video', { type: 'my' })
    ])
    
    userStats.value = {
      total_water_days: waterStats.data.total_days || 0,
      total_water_amount: Math.round((waterStats.data.total_amount || 0) / 1000),
      achievement_count: achievements.data.unlocked_count || 0,
      video_count: videos.data.total || 0
    }
    
    newAchievementCount.value = achievements.data.recent_unlocked?.length || 0
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 更换头像
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      try {
        uni.showLoading({ title: '上传中...' })
        
        // 上传头像
        const uploadResponse = await request.upload('/user/avatar', res.tempFilePaths[0])
        
        // 更新用户信息
        await request.put('/user', {
          avatar_url: uploadResponse.data.avatar_url
        })
        
        userInfo.value.avatar_url = uploadResponse.data.avatar_url
        
        uni.hideLoading()
        uni.showToast({
          title: '头像更新成功',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        console.error('头像上传失败:', error)
        uni.showToast({
          title: '头像上传失败',
          icon: 'error'
        })
      }
    }
  })
}

// 编辑个人资料
const editProfile = () => {
  uni.navigateTo({ url: '/pages/profile/edit' })
}

// 绑定情侣
const bindCouple = () => {
  uni.navigateTo({ url: '/pages/couple/bind' })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地数据
        request.clearToken()
        uni.removeStorageSync('userInfo')
        
        // 跳转到登录页
        uni.reLaunch({ url: '/pages/auth/login' })
      }
    }
  })
}

// 页面跳转方法
const goToStatistics = () => {
  uni.navigateTo({ url: '/pages/statistics/statistics' })
}

const goToAchievements = () => {
  uni.navigateTo({ url: '/pages/achievement/achievement' })
}

const goToChallenges = () => {
  uni.navigateTo({ url: '/pages/challenge/challenge' })
}

const goToSettings = () => {
  uni.navigateTo({ url: '/pages/settings/settings' })
}

const goToHelp = () => {
  uni.navigateTo({ url: '/pages/help/help' })
}

const goToAbout = () => {
  uni.navigateTo({ url: '/pages/about/about' })
}

// 格式化加入日期
const formatJoinDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')} 加入`
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
  padding: 0 32rpx 32rpx;
}

/* 用户信息卡片样式 */
.user-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(255, 182, 193, 0.2);
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.avatar-section {
  position: relative;
  margin-right: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #FFB6C1;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background: #FF69B4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
}

.edit-icon {
  width: 20rpx;
  height: 20rpx;
}

.user-info {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-id {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.join-date {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.edit-profile {
  padding: 16rpx;
}

.edit-profile .edit-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 情侣状态样式 */
.couple-status {
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
  border-radius: 24rpx;
  padding: 24rpx;
  border: 2rpx solid #FFB6C1;
}

.couple-info {
  display: flex;
  align-items: center;
}

.partner-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
  margin-right: 16rpx;
}

.couple-text {
  flex: 1;
}

.couple-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 8rpx;
}

.together-days {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.no-couple {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  background: #FFF0F5;
  border-radius: 24rpx;
  border: 2rpx dashed #FFB6C1;
  transition: all 0.3s ease;
}

.no-couple:active {
  background: #FFE4E1;
}

.heart-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.no-couple text {
  font-size: 28rpx;
  color: #FF69B4;
}

/* 数据统计卡片样式 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
  border-radius: 16rpx;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单样式 */
.menu-section {
  margin-bottom: 32rpx;
}

.menu-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #FFF0F5;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #FFF0F5;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.menu-badge {
  background: #FF69B4;
  color: white;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.menu-badge text {
  font-size: 20rpx;
  font-weight: bold;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 退出登录按钮样式 */
.logout-section {
  padding: 0 16rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FFA07A, #FFB6C1);
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(255, 160, 122, 0.3);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 160, 122, 0.4);
}
</style>
