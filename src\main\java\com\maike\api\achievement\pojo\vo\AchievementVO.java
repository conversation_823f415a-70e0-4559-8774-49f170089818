package com.maike.api.achievement.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成就响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class AchievementVO {

    /**
     * 成就ID
     */
    private String id;

    /**
     * 成就名称
     */
    private String name;

    /**
     * 成就描述
     */
    private String description;

    /**
     * 成就图标URL地址
     */
    private String iconUrl;

    /**
     * 成就类型：personal-个人成就，couple-情侣成就，system-系统成就
     */
    private String type;

    /**
     * 成就类型显示文本
     */
    private String typeText;

    /**
     * 成就分类：water-喝水相关，video-视频相关，social-社交相关
     */
    private String category;

    /**
     * 成就分类显示文本
     */
    private String categoryText;

    /**
     * 成就积分
     */
    private Integer points;

    /**
     * 稀有度：common-普通，rare-稀有，epic-史诗，legendary-传说
     */
    private String rarity;

    /**
     * 稀有度显示文本
     */
    private String rarityText;

    /**
     * 稀有度颜色
     */
    private String rarityColor;

    /**
     * 是否隐藏成就
     */
    private Boolean isHidden;

    /**
     * 解锁顺序
     */
    private Integer unlockOrder;

    /**
     * 前置成就ID
     */
    private String prerequisiteId;

    /**
     * 奖励类型：theme-主题，sticker-贴纸，title-称号
     */
    private String rewardType;

    /**
     * 奖励类型显示文本
     */
    private String rewardTypeText;

    /**
     * 奖励内容
     */
    private String rewardValue;

    /**
     * 是否已解锁
     */
    private Boolean isUnlocked;

    /**
     * 解锁时间
     */
    private LocalDateTime unlockedAt;

    /**
     * 完成进度，百分比
     */
    private BigDecimal progress;

    /**
     * 进度百分比（整数）
     */
    private Integer progressPercent;

    /**
     * 当前数值
     */
    private Integer currentValue;

    /**
     * 目标数值
     */
    private Integer targetValue;

    /**
     * 剩余需要的数值
     */
    private Integer remainingValue;

    /**
     * 是否接近完成
     */
    private Boolean isNearCompletion;

    /**
     * 是否已通知
     */
    private Boolean isNotified;

    /**
     * 解锁来源：auto-自动解锁，manual-手动授予
     */
    private String unlockSource;

    /**
     * 解锁来源显示文本
     */
    private String unlockSourceText;
}
