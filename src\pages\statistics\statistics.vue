<template>
  <view class="statistics-page">
    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <view class="range-tabs">
        <view 
          class="range-tab" 
          v-for="range in timeRanges" 
          :key="range.key"
          :class="{ active: currentRange === range.key }"
          @click="switchTimeRange(range.key)"
        >
          <text>{{ range.label }}</text>
        </view>
      </view>
      
      <view class="custom-date" v-if="currentRange === 'custom'">
        <picker mode="date" :value="customStartDate" @change="onStartDateChange">
          <view class="date-picker">
            <text>{{ customStartDate || '开始日期' }}</text>
          </view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" :value="customEndDate" @change="onEndDateChange">
          <view class="date-picker">
            <text>{{ customEndDate || '结束日期' }}</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 总体统计卡片 -->
    <view class="overview-stats">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ overviewStats.total_amount }}</text>
          <text class="stats-unit">L</text>
          <text class="stats-label">总喝水量</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ overviewStats.avg_daily }}</text>
          <text class="stats-unit">ml</text>
          <text class="stats-label">日均水量</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ overviewStats.goal_achieved_days }}</text>
          <text class="stats-unit">天</text>
          <text class="stats-label">达标天数</text>
        </view>
      </view>
      
      <view class="achievement-rate">
        <view class="rate-circle">
          <view class="rate-progress" :style="{ transform: `rotate(${overviewStats.achievement_rate * 3.6}deg)` }"></view>
          <view class="rate-inner">
            <text class="rate-percent">{{ overviewStats.achievement_rate }}%</text>
            <text class="rate-label">达标率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势图表 -->
    <view class="trend-chart">
      <view class="chart-header">
        <text class="chart-title">喝水趋势</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color actual"></view>
            <text class="legend-text">实际</text>
          </view>
          <view class="legend-item">
            <view class="legend-color target"></view>
            <text class="legend-text">目标</text>
          </view>
        </view>
      </view>
      
      <scroll-view class="chart-container" scroll-x>
        <view class="chart-content" :style="{ width: chartData.length * 120 + 'rpx' }">
          <view class="chart-item" v-for="(item, index) in chartData" :key="index">
            <view class="chart-bars">
              <view class="chart-bar target" :style="{ height: (item.goal / maxValue) * 200 + 'rpx' }"></view>
              <view class="chart-bar actual" :style="{ height: (item.amount / maxValue) * 200 + 'rpx' }"></view>
            </view>
            <text class="chart-date">{{ formatChartDate(item.date) }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 详细数据 -->
    <view class="detailed-stats">
      <view class="section-header">
        <text class="section-title">详细数据</text>
        <view class="export-btn" @click="exportData">
          <image class="export-icon" src="/static/icons/export.png"></image>
          <text>导出</text>
        </view>
      </view>
      
      <view class="stats-list">
        <view class="stats-item-detail" v-for="item in detailedStats" :key="item.date">
          <view class="item-date">
            <text class="date-text">{{ formatDetailDate(item.date) }}</text>
            <text class="weekday-text">{{ getWeekday(item.date) }}</text>
          </view>
          
          <view class="item-data">
            <view class="data-row">
              <text class="data-label">喝水量</text>
              <text class="data-value">{{ item.amount }}ml</text>
            </view>
            <view class="data-row">
              <text class="data-label">目标</text>
              <text class="data-value">{{ item.goal }}ml</text>
            </view>
            <view class="data-row">
              <text class="data-label">完成率</text>
              <text class="data-value" :class="{ achieved: item.completion_rate >= 100 }">
                {{ item.completion_rate }}%
              </text>
            </view>
            <view class="data-row">
              <text class="data-label">打卡次数</text>
              <text class="data-value">{{ item.records_count }}次</text>
            </view>
          </view>
          
          <view class="item-status">
            <view class="status-badge" :class="{ achieved: item.is_achieved }">
              <text>{{ item.is_achieved ? '已达标' : '未达标' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分析洞察 -->
    <view class="insights-section">
      <view class="section-header">
        <text class="section-title">分析洞察</text>
      </view>
      
      <view class="insights-list">
        <view class="insight-item" v-for="insight in insights" :key="insight.type">
          <image class="insight-icon" :src="insight.icon"></image>
          <view class="insight-content">
            <text class="insight-title">{{ insight.title }}</text>
            <text class="insight-desc">{{ insight.description }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import request from '@/utils/request'

// 响应式数据
const currentRange = ref('week')
const customStartDate = ref('')
const customEndDate = ref('')

const overviewStats = ref({
  total_amount: 0,
  avg_daily: 0,
  goal_achieved_days: 0,
  total_days: 0,
  achievement_rate: 0
})

const chartData = ref([])
const detailedStats = ref([])
const insights = ref([])

// 时间范围选项
const timeRanges = ref([
  { key: 'week', label: '本周' },
  { key: 'month', label: '本月' },
  { key: 'quarter', label: '本季度' },
  { key: 'year', label: '本年' },
  { key: 'custom', label: '自定义' }
])

// 计算属性：图表最大值
const maxValue = computed(() => {
  if (chartData.value.length === 0) return 2000
  const maxAmount = Math.max(...chartData.value.map(item => item.amount))
  const maxGoal = Math.max(...chartData.value.map(item => item.goal))
  return Math.max(maxAmount, maxGoal, 2000)
})

// 监听时间范围变化
watch(currentRange, () => {
  loadStatistics()
})

watch([customStartDate, customEndDate], () => {
  if (currentRange.value === 'custom' && customStartDate.value && customEndDate.value) {
    loadStatistics()
  }
})

// 页面加载时获取数据
onMounted(() => {
  loadStatistics()
  loadInsights()
})

// 切换时间范围
const switchTimeRange = (rangeKey: string) => {
  currentRange.value = rangeKey
}

// 日期选择
const onStartDateChange = (e: any) => {
  customStartDate.value = e.detail.value
}

const onEndDateChange = (e: any) => {
  customEndDate.value = e.detail.value
}

// 获取统计数据
const loadStatistics = async () => {
  try {
    const params: any = { type: currentRange.value }
    
    if (currentRange.value === 'custom') {
      params.start_date = customStartDate.value
      params.end_date = customEndDate.value
    }
    
    const response = await request.get('/water/statistics', params)
    const data = response.data
    
    // 更新总体统计
    overviewStats.value = {
      total_amount: Math.round(data.total_amount / 1000 * 10) / 10, // 转换为升，保留一位小数
      avg_daily: Math.round(data.avg_daily_amount),
      goal_achieved_days: data.goal_achieved_days,
      total_days: data.total_days,
      achievement_rate: Math.round(data.achievement_rate)
    }
    
    // 更新图表数据
    chartData.value = data.daily_data || []
    
    // 更新详细数据
    detailedStats.value = data.daily_data?.map(item => ({
      ...item,
      completion_rate: Math.round((item.amount / item.goal) * 100)
    })) || []
    
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取分析洞察
const loadInsights = async () => {
  try {
    // 这里可以调用分析接口，暂时使用模拟数据
    insights.value = [
      {
        type: 'best_time',
        title: '最佳喝水时间',
        description: '上午9-11点是您喝水最多的时段',
        icon: '/static/icons/clock.png'
      },
      {
        type: 'streak',
        title: '连续打卡',
        description: '您已连续5天完成喝水目标，继续保持！',
        icon: '/static/icons/fire.png'
      },
      {
        type: 'improvement',
        title: '进步趋势',
        description: '相比上周，您的日均喝水量增加了15%',
        icon: '/static/icons/trend-up.png'
      }
    ]
  } catch (error) {
    console.error('获取分析洞察失败:', error)
  }
}

// 导出数据
const exportData = () => {
  uni.showActionSheet({
    itemList: ['导出为Excel', '导出为PDF', '分享数据'],
    success: (res) => {
      const actions = ['excel', 'pdf', 'share']
      const action = actions[res.tapIndex]
      
      switch (action) {
        case 'excel':
          exportToExcel()
          break
        case 'pdf':
          exportToPDF()
          break
        case 'share':
          shareData()
          break
      }
    }
  })
}

const exportToExcel = () => {
  uni.showToast({
    title: '导出功能开发中',
    icon: 'none'
  })
}

const exportToPDF = () => {
  uni.showToast({
    title: '导出功能开发中',
    icon: 'none'
  })
}

const shareData = () => {
  const summary = `我在嘉依打卡的喝水数据：总计${overviewStats.value.total_amount}L，日均${overviewStats.value.avg_daily}ml，达标率${overviewStats.value.achievement_rate}%`
  
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '',
    title: '嘉依打卡数据分享',
    summary: summary,
    imageUrl: '/static/share-stats.png'
  })
}

// 工具方法
const formatChartDate = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const formatDetailDate = (dateString: string) => {
  const date = new Date(dateString)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

const getWeekday = (dateString: string) => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const date = new Date(dateString)
  return weekdays[date.getDay()]
}
</script>

<style lang="scss" scoped>
.statistics-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
  padding: 0 32rpx 32rpx;
}

/* 时间范围选择样式 */
.time-range-selector {
  padding: 32rpx 0;
}

.range-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 8rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.range-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.range-tab.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
}

.range-tab text {
  font-size: 24rpx;
  font-weight: bold;
}

.range-tab:not(.active) text {
  color: #666;
}

.custom-date {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
  padding: 0 16rpx;
}

.date-picker {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #FFE4E1;
  text-align: center;
  font-size: 24rpx;
  color: #333;
}

.date-separator {
  font-size: 24rpx;
  color: #999;
}

/* 总体统计样式 */
.overview-stats {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.stats-card {
  flex: 2;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #FF69B4;
  line-height: 1;
}

.stats-unit {
  font-size: 24rpx;
  color: #FF69B4;
  margin-left: 4rpx;
}

.stats-label {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-top: 8rpx;
}

.stats-divider {
  width: 1rpx;
  height: 60rpx;
  background: #FFE4E1;
}

.achievement-rate {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.rate-circle {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #FFE4E1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rate-progress {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(#FF69B4 0deg, #FFE4E1 0deg);
  transition: transform 0.5s ease;
}

.rate-inner {
  position: relative;
  z-index: 2;
  background: white;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 182, 193, 0.2);
}

.rate-percent {
  font-size: 24rpx;
  font-weight: bold;
  color: #FF69B4;
  line-height: 1;
}

.rate-label {
  font-size: 16rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 趋势图表样式 */
.trend-chart {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 2rpx;
}

.legend-color.actual {
  background: #FF69B4;
}

.legend-color.target {
  background: #87CEEB;
}

.legend-text {
  font-size: 20rpx;
  color: #666;
}

.chart-container {
  white-space: nowrap;
}

.chart-content {
  display: flex;
  align-items: flex-end;
  height: 240rpx;
  padding-bottom: 40rpx;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 8rpx;
  height: 200rpx;
  margin-bottom: 16rpx;
}

.chart-bar {
  width: 24rpx;
  border-radius: 12rpx 12rpx 0 0;
  transition: height 0.3s ease;
}

.chart-bar.actual {
  background: linear-gradient(to top, #FF69B4, #FFB6C1);
}

.chart-bar.target {
  background: linear-gradient(to top, #87CEEB, #B0E0E6);
}

.chart-date {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

/* 详细数据样式 */
.detailed-stats {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.export-icon {
  width: 20rpx;
  height: 20rpx;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stats-item-detail {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #FFF0F5;
  border-radius: 16rpx;
  border: 1rpx solid #FFE4E1;
}

.item-date {
  min-width: 120rpx;
  text-align: center;
}

.date-text {
  display: block;
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.weekday-text {
  display: block;
  font-size: 20rpx;
  color: #999;
}

.item-data {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-label {
  font-size: 20rpx;
  color: #666;
}

.data-value {
  font-size: 20rpx;
  color: #333;
  font-weight: bold;
}

.data-value.achieved {
  color: #FF69B4;
}

.item-status {
  min-width: 80rpx;
  text-align: center;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  background: #E0E0E0;
  color: #999;
}

.status-badge.achieved {
  background: linear-gradient(135deg, #98FB98, #90EE90);
  color: #228B22;
}

/* 分析洞察样式 */
.insights-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #FFF0F5;
  border-radius: 16rpx;
}

.insight-icon {
  width: 40rpx;
  height: 40rpx;
}

.insight-content {
  flex: 1;
}

.insight-title {
  display: block;
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.insight-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}
</style>
