#!/bin/bash

# 嘉依打卡后端部署脚本
# 作者: Maike Team
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建应用
build_app() {
    log_info "开始构建应用..."
    
    # 清理并编译
    mvn clean package -DskipTests
    
    if [ $? -eq 0 ]; then
        log_success "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p mysql/conf
    mkdir -p nginx/conf.d
    mkdir -p nginx/ssl
    
    log_success "目录创建完成"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，从.env.example复制..."
        cp .env.example .env
        log_warning "请编辑.env文件，配置正确的环境变量"
        read -p "是否现在编辑.env文件？(y/n): " edit_env
        if [ "$edit_env" = "y" ]; then
            ${EDITOR:-nano} .env
        fi
    fi
}

# 部署应用
deploy_app() {
    log_info "开始部署应用..."
    
    # 停止现有容器
    docker-compose down
    
    # 构建并启动容器
    docker-compose up -d --build
    
    if [ $? -eq 0 ]; then
        log_success "应用部署成功"
    else
        log_error "应用部署失败"
        exit 1
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    sleep 10
    
    # 检查容器状态
    docker-compose ps
    
    # 检查应用健康状态
    log_info "等待应用启动..."
    for i in {1..30}; do
        if curl -f http://localhost:8888/api/v1/actuator/health &> /dev/null; then
            log_success "应用启动成功，健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "应用启动超时，请检查日志"
            docker-compose logs app
            exit 1
        fi
        
        sleep 2
    done
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo ""
    echo "应用信息："
    echo "  - 应用地址: http://localhost:8888/api/v1"
    echo "  - API文档: http://localhost:8888/api/v1/swagger-ui.html"
    echo "  - 健康检查: http://localhost:8888/api/v1/actuator/health"
    echo ""
    echo "管理命令："
    echo "  - 查看日志: docker-compose logs -f app"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo ""
}

# 主函数
main() {
    log_info "开始部署嘉依打卡后端应用..."
    
    check_dependencies
    create_directories
    check_env_file
    build_app
    deploy_app
    check_services
    show_deployment_info
    
    log_success "部署流程完成！"
}

# 执行主函数
main "$@"
