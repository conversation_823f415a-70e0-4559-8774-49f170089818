package com.maike.api.couple.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 发送情侣提醒请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class CoupleRemindDTO {

    /**
     * 提醒消息
     */
    @NotBlank(message = "提醒消息不能为空")
    @Size(min = 1, max = 200, message = "提醒消息长度必须在1-200字符之间")
    private String message;

    /**
     * 提醒类型
     */
    @NotBlank(message = "提醒类型不能为空")
    @Pattern(regexp = "^(water_reminder|love_message|custom)$", message = "提醒类型不正确")
    private String type;
}
