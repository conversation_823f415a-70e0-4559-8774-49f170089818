<template>
  <view class="test-page">
    <view class="header">
      <text class="title">API连接测试</text>
    </view>
    
    <view class="test-section">
      <view class="section-title">环境信息</view>
      <view class="info-item">
        <text class="label">当前环境:</text>
        <text class="value">{{ envInfo.env }}</text>
      </view>
      <view class="info-item">
        <text class="label">API地址:</text>
        <text class="value">{{ envInfo.config.API_BASE_URL }}</text>
      </view>
      <view class="info-item">
        <text class="label">调试模式:</text>
        <text class="value">{{ envInfo.config.DEBUG ? '开启' : '关闭' }}</text>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">API测试</view>
      
      <button 
        class="test-btn" 
        @click="testHealthCheck"
        :disabled="loading"
      >
        {{ loading ? '测试中...' : '健康检查' }}
      </button>
      
      <button 
        class="test-btn" 
        @click="testUserInfo"
        :disabled="loading"
      >
        {{ loading ? '测试中...' : '获取用户信息' }}
      </button>
      
      <button 
        class="test-btn" 
        @click="testSendCode"
        :disabled="loading"
      >
        {{ loading ? '测试中...' : '发送验证码' }}
      </button>
    </view>

    <view class="result-section" v-if="testResult">
      <view class="section-title">测试结果</view>
      <view class="result-content">
        <text class="result-text">{{ testResult }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ENV_INFO } from '@/config/env'
import request from '@/utils/request'

// 响应式数据
const loading = ref(false)
const testResult = ref('')
const envInfo = ref(ENV_INFO)

// 健康检查测试
const testHealthCheck = async () => {
  loading.value = true
  testResult.value = ''
  
  try {
    const response = await request.get('/actuator/health', {}, { showLoading: false })
    testResult.value = `✅ 健康检查成功\n${JSON.stringify(response, null, 2)}`
  } catch (error: any) {
    testResult.value = `❌ 健康检查失败\n错误信息: ${error.message}`
  } finally {
    loading.value = false
  }
}

// 获取用户信息测试
const testUserInfo = async () => {
  loading.value = true
  testResult.value = ''
  
  try {
    const response = await request.get('/user/info', {}, { showLoading: false })
    testResult.value = `✅ 获取用户信息成功\n${JSON.stringify(response, null, 2)}`
  } catch (error: any) {
    testResult.value = `❌ 获取用户信息失败\n错误信息: ${error.message}`
  } finally {
    loading.value = false
  }
}

// 发送验证码测试
const testSendCode = async () => {
  loading.value = true
  testResult.value = ''
  
  try {
    const response = await request.post('/user/send-code', {
      phone: '13800138000',
      type: 'register'
    }, { showLoading: false })
    testResult.value = `✅ 发送验证码成功\n${JSON.stringify(response, null, 2)}`
  } catch (error: any) {
    testResult.value = `❌ 发送验证码失败\n错误信息: ${error.message}`
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  console.log('测试页面加载完成', envInfo.value)
})
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    font-size: 28rpx;
    color: #666;
  }
  
  .value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:disabled {
    background-color: #ccc;
  }
  
  &:active:not(:disabled) {
    background-color: #0056cc;
  }
}

.result-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-content {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  
  .result-text {
    font-size: 24rpx;
    color: #333;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
