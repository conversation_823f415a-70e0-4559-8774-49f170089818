package com.maike.api.common.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger API文档配置类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class SwaggerConfig {

    /**
     * 配置OpenAPI文档信息
     * 
     * @return OpenAPI配置
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server().url("http://localhost:8888/api/v1").description("本地开发环境"),
                        new Server().url("https://api.watertracker.com/api/v1").description("生产环境")
                ))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("Bearer Authentication", createAPIKeyScheme()));
    }

    /**
     * API信息配置
     * 
     * @return API信息
     */
    private Info apiInfo() {
        return new Info()
                .title("嘉依打卡：元气水杯与Ta的约定 API")
                .description("一款专为情侣设计的喝水打卡应用，支持喝水记录、视频分享、成就系统等功能")
                .version("1.0.0")
                .contact(new Contact()
                        .name("Maike Team")
                        .email("<EMAIL>")
                        .url("https://watertracker.com"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }

    /**
     * 创建JWT认证方案
     * 
     * @return JWT认证方案
     */
    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .bearerFormat("JWT")
                .scheme("bearer")
                .description("请输入JWT Token，格式：Bearer {token}");
    }
}
