package com.maike.api.couple.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.maike.api.couple.pojo.entity.Couple;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 情侣关系Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface CoupleMapper extends BaseMapper<Couple> {

    /**
     * 根据邀请码查询情侣关系
     * 
     * @param inviteCode 邀请码
     * @return 情侣关系
     */
    Couple selectByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 根据用户ID查询情侣关系
     * 
     * @param userId 用户ID
     * @return 情侣关系
     */
    Couple selectByUserId(@Param("userId") String userId);

    /**
     * 检查邀请码是否存在
     * 
     * @param inviteCode 邀请码
     * @return 数量
     */
    int countByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 更新情侣关系绑定信息
     * 
     * @param coupleId 情侣关系ID
     * @param user2Id  用户2ID
     * @return 更新行数
     */
    int updateBoundInfo(@Param("coupleId") String coupleId, @Param("user2Id") String user2Id);

    /**
     * 解除情侣关系
     * 
     * @param coupleId 情侣关系ID
     * @return 更新行数
     */
    int unbindCouple(@Param("coupleId") String coupleId);

    /**
     * 更新总天数
     * 
     * @param coupleId  情侣关系ID
     * @param totalDays 总天数
     * @return 更新行数
     */
    int updateTotalDays(@Param("coupleId") String coupleId, @Param("totalDays") Integer totalDays);
}
