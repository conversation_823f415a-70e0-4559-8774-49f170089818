package com.maike.api.challenge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maike.api.challenge.pojo.dto.ChallengeCreateDTO;
import com.maike.api.challenge.pojo.entity.Challenge;
import com.maike.api.challenge.pojo.vo.ChallengeVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 挑战服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ChallengeService extends IService<Challenge> {

    /**
     * 创建挑战
     * 
     * @param userId    用户ID
     * @param createDTO 创建信息
     * @return 挑战信息
     */
    ChallengeVO createChallenge(String userId, ChallengeCreateDTO createDTO);

    /**
     * 获取挑战列表
     * 
     * @param userId 用户ID
     * @param status 挑战状态（可选）
     * @return 挑战列表
     */
    List<ChallengeVO> getChallengeList(String userId, String status);

    /**
     * 获取挑战详情
     * 
     * @param userId      用户ID
     * @param challengeId 挑战ID
     * @return 挑战详情
     */
    ChallengeVO getChallengeDetail(String userId, String challengeId);

    /**
     * 更新挑战进度
     * 
     * @param userId      用户ID
     * @param challengeId 挑战ID
     * @param progress    进度百分比
     */
    void updateChallengeProgress(String userId, String challengeId, BigDecimal progress);

    /**
     * 取消挑战
     * 
     * @param userId      用户ID
     * @param challengeId 挑战ID
     */
    void cancelChallenge(String userId, String challengeId);

    /**
     * 检查并更新挑战状态
     * 
     * @param challengeId 挑战ID
     */
    void checkAndUpdateChallengeStatus(String challengeId);

    /**
     * 获取用户当前活跃的挑战
     * 
     * @param userId 用户ID
     * @return 活跃挑战列表
     */
    List<ChallengeVO> getActiveChallenges(String userId);

    /**
     * 获取挑战统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    ChallengeStatsVO getChallengeStats(String userId);

    /**
     * 挑战统计VO
     */
    class ChallengeStatsVO {
        private Integer totalChallenges;
        private Integer completedChallenges;
        private Integer activeChallenges;
        private Double completionRate;

        // getters and setters
        public Integer getTotalChallenges() { return totalChallenges; }
        public void setTotalChallenges(Integer totalChallenges) { this.totalChallenges = totalChallenges; }
        public Integer getCompletedChallenges() { return completedChallenges; }
        public void setCompletedChallenges(Integer completedChallenges) { this.completedChallenges = completedChallenges; }
        public Integer getActiveChallenges() { return activeChallenges; }
        public void setActiveChallenges(Integer activeChallenges) { this.activeChallenges = activeChallenges; }
        public Double getCompletionRate() { return completionRate; }
        public void setCompletionRate(Double completionRate) { this.completionRate = completionRate; }
    }
}
