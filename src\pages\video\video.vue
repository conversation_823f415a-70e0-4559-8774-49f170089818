<template>
  <view class="video-page">
    <!-- 顶部导航 -->
    <view class="video-nav">
      <view class="nav-tabs">
        <view 
          class="nav-tab" 
          v-for="tab in navTabs" 
          :key="tab.key"
          :class="{ active: currentTab === tab.key }"
          @click="switchTab(tab.key)"
        >
          <text>{{ tab.label }}</text>
        </view>
      </view>
      
      <view class="record-btn" @click="startRecord">
        <image class="record-icon" src="/static/icons/camera-plus.png"></image>
      </view>
    </view>

    <!-- 视频列表 -->
    <view class="video-list">
      <view class="video-item" v-for="video in videoList" :key="video.id" @click="playVideo(video)">
        <view class="video-thumbnail-container">
          <image class="video-thumbnail" :src="video.thumbnail_url" mode="aspectFill"></image>
          <view class="video-duration">
            <text>{{ formatDuration(video.duration_seconds) }}</text>
          </view>
          <view class="play-overlay">
            <image class="play-icon" src="/static/icons/play.png"></image>
          </view>
        </view>
        
        <view class="video-info">
          <view class="video-header">
            <view class="user-info">
              <image class="user-avatar" :src="video.user_info.avatar_url" mode="aspectFill"></image>
              <text class="username">{{ video.user_info.nickname }}</text>
            </view>
            <text class="video-time">{{ formatTime(video.create_time) }}</text>
          </view>
          
          <text class="video-description">{{ video.description || '分享了一个喝水视频' }}</text>
          
          <view class="video-actions">
            <view class="action-item" @click.stop="toggleLike(video)">
              <image 
                class="action-icon" 
                :src="video.is_liked ? '/static/icons/heart-filled.png' : '/static/icons/heart-empty.png'"
              ></image>
              <text class="action-count">{{ video.like_count }}</text>
            </view>
            
            <view class="action-item" @click.stop="showComments(video)">
              <image class="action-icon" src="/static/icons/comment.png"></image>
              <text class="action-count">{{ video.comment_count }}</text>
            </view>
            
            <view class="action-item" @click.stop="shareVideo(video)">
              <image class="action-icon" src="/static/icons/share.png"></image>
              <text class="action-text">分享</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <uni-load-more :status="loadStatus"></uni-load-more>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="videoList.length === 0 && !loading">
        <image class="empty-image" src="/static/illustrations/video-empty.png" mode="aspectFit"></image>
        <text class="empty-text">还没有视频哦</text>
        <text class="empty-desc">快来拍摄第一个喝水视频吧！</text>
      </view>
    </view>

    <!-- 视频播放弹窗 -->
    <uni-popup ref="videoPlayerPopup" type="center">
      <view class="video-player-container">
        <video 
          class="video-player"
          :src="currentVideo.video_url"
          :poster="currentVideo.thumbnail_url"
          controls
          autoplay
          @ended="onVideoEnded"
        ></video>
        
        <view class="player-close" @click="closeVideoPlayer">
          <image class="close-icon" src="/static/icons/close.png"></image>
        </view>
      </view>
    </uni-popup>

    <!-- 评论弹窗 -->
    <uni-popup ref="commentsPopup" type="bottom">
      <view class="comments-container">
        <view class="comments-header">
          <text class="comments-title">评论 {{ currentVideo.comment_count }}</text>
          <view class="close-comments" @click="closeComments">
            <image class="close-icon" src="/static/icons/close.png"></image>
          </view>
        </view>
        
        <scroll-view class="comments-list" scroll-y>
          <view class="comment-item" v-for="comment in commentList" :key="comment.id">
            <image class="comment-avatar" :src="comment.user_info.avatar_url" mode="aspectFill"></image>
            <view class="comment-content">
              <text class="comment-username">{{ comment.user_info.nickname }}</text>
              <text class="comment-text">{{ comment.comment_text }}</text>
              <text class="comment-time">{{ formatTime(comment.create_time) }}</text>
            </view>
          </view>
        </scroll-view>
        
        <view class="comment-input-section">
          <input 
            class="comment-input" 
            v-model="newComment" 
            placeholder="说点什么..."
            @confirm="submitComment"
          />
          <button class="send-comment-btn" @click="submitComment" :disabled="!newComment.trim()">
            发送
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onReachBottom } from 'vue'
import request from '@/utils/request'

// 响应式数据
const currentTab = ref('couple')
const videoList = ref([])
const currentVideo = ref({})
const commentList = ref([])
const newComment = ref('')
const loading = ref(false)
const hasMore = ref(true)
const loadStatus = ref('more')
const page = ref(1)

// 导航标签
const navTabs = ref([
  { key: 'couple', label: '情侣' },
  { key: 'my', label: '我的' },
  { key: 'public', label: '广场' }
])

// 弹窗引用
const videoPlayerPopup = ref()
const commentsPopup = ref()

// 页面加载时获取数据
onMounted(() => {
  loadVideoList()
})

// 触底加载更多
onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    loadMoreVideos()
  }
})

// 切换标签
const switchTab = (tabKey: string) => {
  currentTab.value = tabKey
  page.value = 1
  videoList.value = []
  hasMore.value = true
  loadVideoList()
}

// 加载视频列表
const loadVideoList = async () => {
  if (loading.value) return
  
  loading.value = true
  loadStatus.value = 'loading'
  
  try {
    const response = await request.get('/video', {
      type: currentTab.value,
      page: page.value,
      size: 10
    })
    
    if (page.value === 1) {
      videoList.value = response.data.videos
    } else {
      videoList.value.push(...response.data.videos)
    }
    
    hasMore.value = response.data.videos.length === 10
    loadStatus.value = hasMore.value ? 'more' : 'noMore'
  } catch (error) {
    console.error('获取视频列表失败:', error)
    loadStatus.value = 'more'
  } finally {
    loading.value = false
  }
}

// 加载更多视频
const loadMoreVideos = () => {
  page.value++
  loadVideoList()
}

// 开始录制
const startRecord = () => {
  uni.chooseVideo({
    sourceType: ['camera'],
    maxDuration: 15,
    camera: 'front',
    success: (res) => {
      // 跳转到视频编辑页面
      uni.navigateTo({
        url: `/pages/video/edit?videoPath=${encodeURIComponent(res.tempFilePath)}`
      })
    },
    fail: (error) => {
      console.error('录制失败:', error)
      uni.showToast({
        title: '录制失败',
        icon: 'error'
      })
    }
  })
}

// 播放视频
const playVideo = (video: any) => {
  currentVideo.value = video
  videoPlayerPopup.value.open()
  
  // 增加播放次数
  incrementViewCount(video.id)
}

// 关闭视频播放器
const closeVideoPlayer = () => {
  videoPlayerPopup.value.close()
}

// 视频播放结束
const onVideoEnded = () => {
  // 可以在这里处理播放结束的逻辑
}

// 增加播放次数
const incrementViewCount = async (videoId: string) => {
  try {
    await request.post(`/video/${videoId}/view`)
  } catch (error) {
    console.error('增加播放次数失败:', error)
  }
}

// 切换点赞
const toggleLike = async (video: any) => {
  try {
    const response = await request.post(`/video/${video.id}/like`)
    
    // 更新本地数据
    video.is_liked = response.data.is_liked
    video.like_count = response.data.like_count
  } catch (error) {
    console.error('点赞失败:', error)
  }
}

// 显示评论
const showComments = async (video: any) => {
  currentVideo.value = video
  await loadComments(video.id)
  commentsPopup.value.open()
}

// 关闭评论
const closeComments = () => {
  commentsPopup.value.close()
  newComment.value = ''
}

// 加载评论
const loadComments = async (videoId: string) => {
  try {
    const response = await request.get(`/video/${videoId}/comment`)
    commentList.value = response.data.comments
  } catch (error) {
    console.error('获取评论失败:', error)
  }
}

// 提交评论
const submitComment = async () => {
  if (!newComment.value.trim()) return
  
  try {
    const response = await request.post(`/video/${currentVideo.value.id}/comment`, {
      comment_text: newComment.value.trim()
    })
    
    // 更新评论列表
    await loadComments(currentVideo.value.id)
    
    // 更新视频的评论数
    const videoIndex = videoList.value.findIndex(v => v.id === currentVideo.value.id)
    if (videoIndex !== -1) {
      videoList.value[videoIndex].comment_count = response.data.comment_count
    }
    
    newComment.value = ''
    
    uni.showToast({
      title: '评论成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('评论失败:', error)
    uni.showToast({
      title: '评论失败',
      icon: 'error'
    })
  }
}

// 分享视频
const shareVideo = (video: any) => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '',
    title: '嘉依打卡视频分享',
    summary: video.description || '我在嘉依打卡分享了一个喝水视频',
    imageUrl: video.thumbnail_url
  })
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 格式化时间
const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return `${Math.floor(minutes / 1440)}天前`
}
</script>

<style lang="scss" scoped>
.video-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
}

/* 顶部导航样式 */
.video-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.1);
}

.nav-tabs {
  display: flex;
  gap: 32rpx;
}

.nav-tab {
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.nav-tab.active {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
}

.nav-tab:not(.active) {
  color: #666;
}

.nav-tab text {
  font-size: 28rpx;
  font-weight: bold;
}

.record-btn {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
}

.record-btn:active {
  transform: scale(0.95);
}

.record-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 视频列表样式 */
.video-list {
  padding: 0 32rpx 32rpx;
}

.video-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.15);
  transition: all 0.3s ease;
}

.video-item:active {
  transform: scale(0.98);
}

.video-thumbnail-container {
  position: relative;
  width: 100%;
  height: 400rpx;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
}

.video-duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.play-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 4rpx;
}

.video-info {
  padding: 24rpx;
}

.video-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
}

.username {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.video-time {
  font-size: 20rpx;
  color: #999;
}

.video-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.video-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

.action-count, .action-text {
  font-size: 24rpx;
  color: #666;
}

/* 加载更多样式 */
.load-more {
  padding: 32rpx 0;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 视频播放器样式 */
.video-player-container {
  position: relative;
  width: 90vw;
  max-width: 600rpx;
  background: black;
  border-radius: 16rpx;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 400rpx;
}

.player-close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 评论弹窗样式 */
.comments-container {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.comments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #FFE4E1;
}

.comments-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-comments {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comments-list {
  flex: 1;
  padding: 0 32rpx;
}

.comment-item {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #FFF0F5;
}

.comment-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
}

.comment-content {
  flex: 1;
}

.comment-username {
  display: block;
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.comment-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.comment-time {
  display: block;
  font-size: 20rpx;
  color: #999;
}

.comment-input-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #FFE4E1;
  background: #FFF0F5;
}

.comment-input {
  flex: 1;
  height: 64rpx;
  background: white;
  border-radius: 32rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  border: 2rpx solid #FFE4E1;
}

.send-comment-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.send-comment-btn:disabled {
  background: #E0E0E0;
  color: #999;
}
</style>
