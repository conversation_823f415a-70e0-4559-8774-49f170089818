package com.maike.api.achievement.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户成就实体类
 * 对应数据库表：user_achievements
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_achievements")
public class UserAchievement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID，关联users表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 成就ID，关联achievements表
     */
    @TableField("achievement_id")
    private String achievementId;

    /**
     * 解锁时间
     */
    @TableField("unlocked_at")
    private LocalDateTime unlockedAt;

    /**
     * 完成进度，百分比，100.00表示已完成
     */
    @TableField("progress")
    private BigDecimal progress;

    /**
     * 当前数值，如连续打卡天数
     */
    @TableField("current_value")
    private Integer currentValue;

    /**
     * 目标数值，如需要连续打卡7天
     */
    @TableField("target_value")
    private Integer targetValue;

    /**
     * 是否已通知：1-已通知，0-未通知
     */
    @TableField("is_notified")
    private Boolean isNotified;

    /**
     * 解锁来源：auto-自动解锁，manual-手动授予
     */
    @TableField("unlock_source")
    private String unlockSource;

    /**
     * 记录状态：0-正常，1-已删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断记录是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.status);
    }

    /**
     * 判断记录是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.status);
    }

    /**
     * 判断成就是否已解锁
     * 
     * @return true-已解锁，false-未解锁
     */
    public boolean isUnlocked() {
        return this.unlockedAt != null && this.progress != null && 
               this.progress.compareTo(BigDecimal.valueOf(100)) >= 0;
    }

    /**
     * 判断是否为自动解锁
     * 
     * @return true-自动解锁，false-手动授予
     */
    public boolean isAutoUnlock() {
        return "auto".equals(this.unlockSource);
    }

    /**
     * 判断是否为手动授予
     * 
     * @return true-手动授予，false-自动解锁
     */
    public boolean isManualUnlock() {
        return "manual".equals(this.unlockSource);
    }

    /**
     * 获取进度百分比（整数）
     * 
     * @return 进度百分比
     */
    public Integer getProgressPercent() {
        if (progress == null) {
            return 0;
        }
        return progress.intValue();
    }

    /**
     * 更新进度
     * 
     * @param currentValue 当前数值
     * @param targetValue  目标数值
     */
    public void updateProgress(Integer currentValue, Integer targetValue) {
        this.currentValue = currentValue;
        this.targetValue = targetValue;
        
        if (targetValue != null && targetValue > 0 && currentValue != null) {
            BigDecimal progressValue = BigDecimal.valueOf(currentValue)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(targetValue), 2, BigDecimal.ROUND_HALF_UP);
            
            // 进度不能超过100%
            this.progress = progressValue.compareTo(BigDecimal.valueOf(100)) > 0 ? 
                    BigDecimal.valueOf(100) : progressValue;
        } else {
            this.progress = BigDecimal.ZERO;
        }
    }

    /**
     * 检查是否达成成就
     * 
     * @return true-达成，false-未达成
     */
    public boolean checkAchievement() {
        if (currentValue == null || targetValue == null || targetValue <= 0) {
            return false;
        }
        
        return currentValue >= targetValue;
    }

    /**
     * 解锁成就
     */
    public void unlock() {
        this.unlockedAt = LocalDateTime.now();
        this.progress = BigDecimal.valueOf(100);
        this.isNotified = false; // 需要通知用户
    }

    /**
     * 标记为已通知
     */
    public void markAsNotified() {
        this.isNotified = true;
    }

    /**
     * 获取解锁来源显示文本
     * 
     * @return 解锁来源显示文本
     */
    public String getUnlockSourceText() {
        if (unlockSource == null) {
            return "自动解锁";
        }
        
        switch (unlockSource) {
            case "auto":
                return "自动解锁";
            case "manual":
                return "手动授予";
            default:
                return "自动解锁";
        }
    }

    /**
     * 获取剩余需要的数值
     * 
     * @return 剩余需要的数值
     */
    public Integer getRemainingValue() {
        if (targetValue == null || currentValue == null) {
            return targetValue != null ? targetValue : 0;
        }
        
        int remaining = targetValue - currentValue;
        return Math.max(remaining, 0);
    }

    /**
     * 判断是否接近完成（进度超过80%）
     * 
     * @return true-接近完成，false-未接近完成
     */
    public boolean isNearCompletion() {
        if (progress == null) {
            return false;
        }
        return progress.compareTo(BigDecimal.valueOf(80)) >= 0;
    }
}
