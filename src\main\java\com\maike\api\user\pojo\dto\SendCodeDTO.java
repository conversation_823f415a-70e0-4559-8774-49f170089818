package com.maike.api.user.pojo.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 发送验证码请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class SendCodeDTO {

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

    /**
     * 验证码类型：register-注册，login-登录，reset-重置密码，change-修改密码
     */
    @NotBlank(message = "验证码类型不能为空")
    @Pattern(regexp = "^(register|login|reset|change)$", message = "验证码类型不正确")
    private String type;
}
