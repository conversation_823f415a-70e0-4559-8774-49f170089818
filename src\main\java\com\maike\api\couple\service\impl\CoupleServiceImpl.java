package com.maike.api.couple.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maike.api.common.exception.BusinessException;
import com.maike.api.common.result.ResultCode;
import com.maike.api.couple.mapper.CoupleMapper;
import com.maike.api.couple.pojo.dto.CoupleAcceptDTO;
import com.maike.api.couple.pojo.dto.CoupleCreateDTO;
import com.maike.api.couple.pojo.dto.CoupleUpdateDTO;
import com.maike.api.couple.pojo.entity.Couple;
import com.maike.api.couple.pojo.vo.CoupleCreateVO;
import com.maike.api.couple.pojo.vo.CoupleInfoVO;
import com.maike.api.couple.service.CoupleService;
import com.maike.api.user.pojo.entity.User;
import com.maike.api.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 情侣关系服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoupleServiceImpl extends ServiceImpl<CoupleMapper, Couple> implements CoupleService {

    private final CoupleMapper coupleMapper;
    private final UserService userService;

    /**
     * 邀请码过期时间（小时）
     */
    private static final int INVITE_CODE_EXPIRE_HOURS = 24;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoupleCreateVO createInvite(String userId, CoupleCreateDTO createDTO) {
        log.info("创建情侣邀请，用户ID：{}", userId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 检查用户是否已有情侣关系
        if (hasCouple(userId)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "您已有情侣关系，无法创建新的邀请");
        }

        // 3. 生成唯一邀请码
        String inviteCode = generateInviteCode();

        // 4. 创建情侣关系记录
        Couple couple = new Couple();
        couple.setInviteCode(inviteCode);
        couple.setUser1Id(userId);
        couple.setCoupleName(createDTO.getCoupleName());
        couple.setAnniversaryDate(createDTO.getAnniversaryDate());
        couple.setIsActive(false); // 初始状态为未激活，等待对方接受
        couple.setStatus("0");
        couple.setCreateBy(userId);
        couple.setUpdateBy(userId);

        // 5. 保存到数据库
        if (!save(couple)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "创建邀请失败");
        }

        // 6. 构建返回结果
        CoupleCreateVO createVO = new CoupleCreateVO();
        createVO.setCoupleId(couple.getId());
        createVO.setInviteCode(inviteCode);
        createVO.setExpiresIn((long) INVITE_CODE_EXPIRE_HOURS * 3600); // 转换为秒

        log.info("情侣邀请创建成功，邀请码：{}", inviteCode);
        return createVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void acceptInvite(String userId, CoupleAcceptDTO acceptDTO) {
        log.info("接受情侣邀请，用户ID：{}，邀请码：{}", userId, acceptDTO.getInviteCode());

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 检查用户是否已有情侣关系
        if (hasCouple(userId)) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "您已有情侣关系，无法接受邀请");
        }

        // 3. 根据邀请码查询情侣关系
        Couple couple = getCoupleByInviteCode(acceptDTO.getInviteCode());
        if (couple == null) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "邀请码不存在或已过期");
        }

        // 4. 检查邀请码状态
        if (couple.isBound()) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "该邀请码已被使用");
        }

        // 5. 检查是否是自己的邀请码
        if (userId.equals(couple.getUser1Id())) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "不能接受自己的邀请");
        }

        // 6. 更新情侣关系
        if (coupleMapper.updateBoundInfo(couple.getId(), userId) <= 0) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "接受邀请失败");
        }

        // 7. 更新用户的情侣关系ID
        User user1 = userService.getById(couple.getUser1Id());
        User user2 = userService.getById(userId);
        
        user1.setCoupleId(couple.getId());
        user2.setCoupleId(couple.getId());
        
        userService.updateById(user1);
        userService.updateById(user2);

        // 8. 如果没有设置情侣昵称，生成默认昵称
        if (StrUtil.isBlank(couple.getCoupleName())) {
            String defaultName = Couple.generateDefaultCoupleName(user1.getNickname(), user2.getNickname());
            couple.setCoupleName(defaultName);
            updateById(couple);
        }

        log.info("情侣邀请接受成功，情侣关系ID：{}", couple.getId());
    }

    @Override
    public CoupleInfoVO getCoupleInfo(String userId) {
        log.info("获取情侣信息，用户ID：{}", userId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询情侣关系
        Couple couple = getCoupleByUserId(userId);
        if (couple == null) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "您还没有情侣关系");
        }

        // 3. 获取伴侣信息
        String partnerUserId = couple.getPartnerUserId(userId);
        User partnerUser = userService.getById(partnerUserId);
        if (partnerUser == null || !partnerUser.isNormal()) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "伴侣信息异常");
        }

        // 4. 构建返回结果
        CoupleInfoVO infoVO = new CoupleInfoVO();
        BeanUtil.copyProperties(couple, infoVO);
        infoVO.setCoupleId(couple.getId());

        // 5. 设置伴侣信息
        CoupleInfoVO.PartnerInfo partnerInfo = new CoupleInfoVO.PartnerInfo();
        partnerInfo.setUserId(partnerUser.getId());
        partnerInfo.setNickname(partnerUser.getNickname());
        partnerInfo.setAvatarUrl(partnerUser.getAvatarUrl());
        partnerInfo.setGender(partnerUser.getGender());
        infoVO.setPartnerInfo(partnerInfo);

        // 6. 更新总天数
        couple.updateTotalDays();
        infoVO.setTotalDays(couple.getTotalDays());
        updateTotalDays(couple.getId());

        return infoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> updateCoupleInfo(String userId, CoupleUpdateDTO updateDTO) {
        log.info("更新情侣信息，用户ID：{}", userId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询情侣关系
        Couple couple = getCoupleByUserId(userId);
        if (couple == null) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "您还没有情侣关系");
        }

        List<String> updatedFields = new ArrayList<>();

        // 3. 更新情侣昵称
        if (StrUtil.isNotBlank(updateDTO.getCoupleName()) && !updateDTO.getCoupleName().equals(couple.getCoupleName())) {
            couple.setCoupleName(updateDTO.getCoupleName());
            updatedFields.add("coupleName");
        }

        // 4. 更新纪念日
        if (updateDTO.getAnniversaryDate() != null && !updateDTO.getAnniversaryDate().equals(couple.getAnniversaryDate())) {
            couple.setAnniversaryDate(updateDTO.getAnniversaryDate());
            couple.updateTotalDays(); // 重新计算总天数
            updatedFields.add("anniversaryDate");
            updatedFields.add("totalDays");
        }

        // 5. 保存更新
        if (!updatedFields.isEmpty()) {
            couple.setUpdateBy(userId);
            if (!updateById(couple)) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "更新情侣信息失败");
            }
        }

        log.info("情侣信息更新成功，更新字段：{}", updatedFields);
        return updatedFields;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindCouple(String userId) {
        log.info("解除情侣关系，用户ID：{}", userId);

        // 1. 检查用户是否存在且状态正常
        if (!userService.isUserExistAndNormal(userId)) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 查询情侣关系
        Couple couple = getCoupleByUserId(userId);
        if (couple == null) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "您还没有情侣关系");
        }

        // 3. 解除情侣关系
        if (coupleMapper.unbindCouple(couple.getId()) <= 0) {
            throw new BusinessException(ResultCode.ERROR.getCode(), "解除情侣关系失败");
        }

        // 4. 清除用户的情侣关系ID
        User user1 = userService.getById(couple.getUser1Id());
        User user2 = userService.getById(couple.getUser2Id());

        if (user1 != null) {
            user1.setCoupleId(null);
            userService.updateById(user1);
        }

        if (user2 != null) {
            user2.setCoupleId(null);
            userService.updateById(user2);
        }

        log.info("情侣关系解除成功，情侣关系ID：{}", couple.getId());
    }

    @Override
    public Couple getCoupleByInviteCode(String inviteCode) {
        return coupleMapper.selectByInviteCode(inviteCode);
    }

    @Override
    public Couple getCoupleByUserId(String userId) {
        return coupleMapper.selectByUserId(userId);
    }

    @Override
    public String generateInviteCode() {
        String inviteCode;
        int maxAttempts = 10;
        int attempts = 0;

        do {
            // 生成6-8位的随机邀请码，包含大写字母和数字
            inviteCode = RandomUtil.randomString("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", 8);
            attempts++;

            if (attempts >= maxAttempts) {
                throw new BusinessException(ResultCode.ERROR.getCode(), "生成邀请码失败，请重试");
            }
        } while (coupleMapper.countByInviteCode(inviteCode) > 0);

        return inviteCode;
    }

    @Override
    public boolean hasCouple(String userId) {
        Couple couple = getCoupleByUserId(userId);
        return couple != null && couple.isActive() && couple.isNormal();
    }

    @Override
    public void updateTotalDays(String coupleId) {
        Couple couple = getById(coupleId);
        if (couple != null && couple.isNormal()) {
            couple.updateTotalDays();
            coupleMapper.updateTotalDays(coupleId, couple.getTotalDays());
        }
    }
}
