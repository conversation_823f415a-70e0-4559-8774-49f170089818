<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maike.api.water.mapper.WaterGoalMapper">

    <!-- 喝水目标基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, daily_goal_ml, goal_date, actual_amount_ml, is_achieved, 
        achievement_time, completion_rate, status, create_by, create_time, update_by, update_time, remark
    </sql>

    <!-- 根据用户ID和日期查询目标 -->
    <select id="selectByUserIdAndDate" resultType="com.maike.api.water.pojo.entity.WaterGoal">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_goals
        WHERE user_id = #{userId}
        AND goal_date = #{goalDate}
        AND status = '0'
        LIMIT 1
    </select>

    <!-- 查询用户指定日期范围内的目标 -->
    <select id="selectUserGoalsByDateRange" resultType="com.maike.api.water.pojo.entity.WaterGoal">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_goals
        WHERE user_id = #{userId}
        AND goal_date BETWEEN #{startDate} AND #{endDate}
        AND status = '0'
        ORDER BY goal_date DESC
    </select>

    <!-- 更新目标的实际完成量 -->
    <update id="updateActualAmount">
        UPDATE water_goals
        SET actual_amount_ml = #{actualAmount},
            completion_rate = ROUND((#{actualAmount} * 100.0 / daily_goal_ml), 2),
            is_achieved = CASE WHEN #{actualAmount} >= daily_goal_ml THEN 1 ELSE 0 END,
            achievement_time = CASE
                WHEN #{actualAmount} >= daily_goal_ml AND is_achieved = 0 THEN NOW()
                WHEN #{actualAmount} &lt; daily_goal_ml THEN NULL
                ELSE achievement_time
            END,
            update_time = NOW()
        WHERE id = #{goalId}
        AND status = '0'
    </update>

    <!-- 更新目标达成状态 -->
    <update id="updateAchievementStatus">
        UPDATE water_goals
        SET is_achieved = #{isAchieved},
            achievement_time = #{achievementTime},
            completion_rate = #{completionRate},
            update_time = NOW()
        WHERE id = #{goalId}
        AND status = '0'
    </update>

    <!-- 查询用户最近的目标 -->
    <select id="selectUserRecentGoals" resultType="com.maike.api.water.pojo.entity.WaterGoal">
        SELECT
        <include refid="Base_Column_List"/>
        FROM water_goals
        WHERE user_id = #{userId}
        AND status = '0'
        ORDER BY goal_date DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户目标达成情况 -->
    <select id="selectUserAchievementStats" resultType="com.maike.api.water.mapper.WaterGoalMapper$GoalAchievementStat">
        SELECT 
            COUNT(*) as totalGoals,
            SUM(CASE WHEN is_achieved = 1 THEN 1 ELSE 0 END) as achievedGoals,
            ROUND(
                (SUM(CASE WHEN is_achieved = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 
                2
            ) as achievementRate
        FROM water_goals
        WHERE user_id = #{userId}
        AND goal_date BETWEEN #{startDate} AND #{endDate}
        AND status = '0'
    </select>

</mapper>
