package com.maike.api.water.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 添加喝水记录响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class WaterRecordCreateVO {

    /**
     * 记录ID
     */
    private String recordId;

    /**
     * 今日总喝水量（毫升）
     */
    private Integer todayTotal;

    /**
     * 目标完成进度（百分比）
     */
    private BigDecimal goalProgress;

    /**
     * 是否达成目标
     */
    private Boolean isGoalAchieved;

    /**
     * 剩余需要喝水量（毫升）
     */
    private Integer remainingAmount;
}
