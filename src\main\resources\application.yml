# 嘉依打卡后端服务配置
server:
  port: 8888
  servlet:
    context-path: /api/v1

spring:
  application:
    name: water-backend

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: root
    password: zyd5495
    hikari:
      # 连接池配置
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: WaterTrackerHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      # password:
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null

# MyBatis-Plus配置
mybatis-plus:
  # 配置扫描路径
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.maike.api.**.pojo.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 配置JdbcTypeForNull
    jdbc-type-for-null: null
  global-config:
    db-config:
      # 主键类型
      id-type: assign_id
      # 逻辑删除配置
      logic-delete-field: status
      logic-delete-value: 2
      logic-not-delete-value: 0
      # 数据库表前缀
      table-prefix:
      # 表名是否使用驼峰转下划线命名
      table-underline: true

# 日志配置
logging:
  level:
    com.maike.api: debug
    org.springframework: warn
    root: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/water-backend.log
    max-size: 10MB
    max-history: 30

# JWT配置
jwt:
  # 密钥
  secret: waterTrackerSecretKey2024ForJiaYiDaKa
  # token有效期（秒）
  expiration: 604800
  # token前缀
  token-prefix: Bearer
  # header名称
  header: Authorization

# 应用自定义配置
app:
  # 文件上传配置
  upload:
    # 上传路径
    path: /uploads/
    # 允许上传的文件类型
    allowed-types: jpg,jpeg,png,gif,mp4,mov,avi
    # 最大文件大小（MB）
    max-size: 100

  # 验证码配置
  sms:
    # 验证码有效期（分钟）
    expire-time: 5
    # 验证码长度
    code-length: 6
