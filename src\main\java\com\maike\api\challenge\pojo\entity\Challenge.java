package com.maike.api.challenge.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 挑战实体类
 * 对应数据库表：challenges
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("challenges")
public class Challenge implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 挑战ID，主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 情侣ID，关联couples表
     */
    @TableField("couple_id")
    private String coupleId;

    /**
     * 挑战名称，如"7天喝水挑战"
     */
    @TableField("name")
    private String name;

    /**
     * 挑战描述，详细说明挑战内容和规则
     */
    @TableField("description")
    private String description;

    /**
     * 挑战类型：water-喝水挑战，video-视频挑战，interaction-互动挑战，custom-自定义
     */
    @TableField("type")
    private String type;

    /**
     * 挑战持续天数
     */
    @TableField("duration_days")
    private Integer durationDays;

    /**
     * 挑战要求，JSON格式存储复杂条件
     */
    @TableField("requirements")
    private String requirements;

    /**
     * 挑战开始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 挑战结束日期
     */
    @TableField("end_date")
    private LocalDate endDate;

    /**
     * 挑战状态：pending-待开始，active-进行中，completed-已完成，failed-已失败，cancelled-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 用户1的完成进度，百分比
     */
    @TableField("user1_progress")
    private BigDecimal user1Progress;

    /**
     * 用户2的完成进度，百分比
     */
    @TableField("user2_progress")
    private BigDecimal user2Progress;

    /**
     * 总体完成进度，百分比
     */
    @TableField("total_progress")
    private BigDecimal totalProgress;

    /**
     * 奖励类型：achievement-成就，theme-主题，title-称号
     */
    @TableField("reward_type")
    private String rewardType;

    /**
     * 奖励内容
     */
    @TableField("reward_value")
    private String rewardValue;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;

    /**
     * 创建挑战的用户ID
     */
    @TableField("created_by_user")
    private String createdByUser;

    /**
     * 是否公开：1-公开，0-私有
     */
    @TableField("is_public")
    private Boolean isPublic;

    /**
     * 记录状态：0-正常，1-已删除
     */
    @TableField("status")
    @TableLogic(value = "0", delval = "1")
    private String recordStatus;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    // ========== 业务方法 ==========

    /**
     * 判断记录是否正常状态
     * 
     * @return true-正常，false-异常
     */
    public boolean isNormal() {
        return "0".equals(this.recordStatus);
    }

    /**
     * 判断记录是否已删除
     * 
     * @return true-已删除，false-未删除
     */
    public boolean isDeleted() {
        return "1".equals(this.recordStatus);
    }

    /**
     * 判断挑战是否待开始
     * 
     * @return true-待开始，false-非待开始
     */
    public boolean isPending() {
        return "pending".equals(this.status);
    }

    /**
     * 判断挑战是否进行中
     * 
     * @return true-进行中，false-非进行中
     */
    public boolean isActive() {
        return "active".equals(this.status);
    }

    /**
     * 判断挑战是否已完成
     * 
     * @return true-已完成，false-未完成
     */
    public boolean isCompleted() {
        return "completed".equals(this.status);
    }

    /**
     * 判断挑战是否已失败
     * 
     * @return true-已失败，false-未失败
     */
    public boolean isFailed() {
        return "failed".equals(this.status);
    }

    /**
     * 判断挑战是否已取消
     * 
     * @return true-已取消，false-未取消
     */
    public boolean isCancelled() {
        return "cancelled".equals(this.status);
    }

    /**
     * 获取挑战状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusText() {
        if (status == null) {
            return "待开始";
        }
        
        switch (status) {
            case "pending":
                return "待开始";
            case "active":
                return "进行中";
            case "completed":
                return "已完成";
            case "failed":
                return "已失败";
            case "cancelled":
                return "已取消";
            default:
                return "待开始";
        }
    }

    /**
     * 获取挑战类型显示文本
     * 
     * @return 类型显示文本
     */
    public String getTypeText() {
        if (type == null) {
            return "喝水挑战";
        }
        
        switch (type) {
            case "water":
                return "喝水挑战";
            case "video":
                return "视频挑战";
            case "interaction":
                return "互动挑战";
            case "custom":
                return "自定义挑战";
            default:
                return "喝水挑战";
        }
    }

    /**
     * 计算挑战剩余天数
     * 
     * @return 剩余天数
     */
    public long getRemainingDays() {
        if (endDate == null) {
            return 0;
        }
        
        LocalDate today = LocalDate.now();
        if (today.isAfter(endDate)) {
            return 0;
        }
        
        return ChronoUnit.DAYS.between(today, endDate);
    }

    /**
     * 计算挑战已进行天数
     * 
     * @return 已进行天数
     */
    public long getElapsedDays() {
        if (startDate == null) {
            return 0;
        }
        
        LocalDate today = LocalDate.now();
        if (today.isBefore(startDate)) {
            return 0;
        }
        
        LocalDate endDateToUse = endDate != null && today.isAfter(endDate) ? endDate : today;
        return ChronoUnit.DAYS.between(startDate, endDateToUse) + 1;
    }

    /**
     * 更新总体进度
     */
    public void updateTotalProgress() {
        if (user1Progress != null && user2Progress != null) {
            this.totalProgress = user1Progress.add(user2Progress)
                    .divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_HALF_UP);
        } else if (user1Progress != null) {
            this.totalProgress = user1Progress;
        } else if (user2Progress != null) {
            this.totalProgress = user2Progress;
        } else {
            this.totalProgress = BigDecimal.ZERO;
        }
    }

    /**
     * 检查是否应该完成挑战
     * 
     * @return true-应该完成，false-不应该完成
     */
    public boolean shouldComplete() {
        return totalProgress != null && totalProgress.compareTo(BigDecimal.valueOf(100)) >= 0;
    }

    /**
     * 检查是否应该失败挑战
     * 
     * @return true-应该失败，false-不应该失败
     */
    public boolean shouldFail() {
        LocalDate today = LocalDate.now();
        return endDate != null && today.isAfter(endDate) && !shouldComplete();
    }
}
