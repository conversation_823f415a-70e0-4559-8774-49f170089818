package com.maike.api.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.maike.api.user.pojo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据手机号查询用户
     * 
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    User selectByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    /**
     * 根据手机号查询用户（包含已删除的用户）
     * 
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    User selectByPhoneNumberWithDeleted(@Param("phoneNumber") String phoneNumber);

    /**
     * 更新用户最后登录信息
     * 
     * @param userId   用户ID
     * @param loginIp  登录IP
     * @return 影响行数
     */
    int updateLoginInfo(@Param("userId") String userId, @Param("loginIp") String loginIp);

    /**
     * 根据昵称查询用户数量
     * 
     * @param nickname 昵称
     * @param excludeUserId 排除的用户ID
     * @return 用户数量
     */
    int countByNickname(@Param("nickname") String nickname, @Param("excludeUserId") String excludeUserId);

    /**
     * 软删除用户
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int softDeleteUser(@Param("userId") String userId);

    /**
     * 恢复已删除的用户
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int restoreUser(@Param("userId") String userId);
}
